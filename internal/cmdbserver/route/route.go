package route

import (
	"net/http"

	"ks-knoc-server/internal/cmdbserver/controller/v1/check"
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"
	"ks-knoc-server/internal/cmdbserver/middleware/audit"
	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/http/middleware"
	"ks-knoc-server/internal/common/http/middleware/auth"
	commonMiddleware "ks-knoc-server/internal/common/middleware"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm/module/apmgin"
	"go.uber.org/zap"
)

// APIServerRouter APIServer路由
func APIServerRouter(g *gin.Engine, db *store.DataStore, mw ...gin.HandlerFunc) *gin.Engine {
	// 应用中间件
	g.Use(apmgin.Middleware(g))
	g.Use(gin.Recovery())
	g.Use(middleware.NoCache)
	g.Use(middleware.Options)
	g.Use(middleware.Secure)
	g.Use(mw...)
	g.NoRoute(func(c *gin.Context) {
		c.String(http.StatusNotFound, "The incorrect API route, Please contact the Kwai IT Developer.")
	})

	// 定义API相关路由
	api := g.Group("/api")
	{
		// 初始化API v1版本的路由
		v1 := api.Group("/v1")
		jwtStrategy, _ := newJwtAuth().(auth.JWTStrategy)
		v1.Use(jwtStrategy.AuthFunc())

		// 初始化审计中间件
		v1.Use(audit.CmdbAuditLog(db))
		{
			// 初始化cmdb的控制器
			cmdbController := cmdb.NewCMDBController(db)

			// 注册各个模块的路由
			RegisterModelGroupRoutes(v1, cmdbController)
			RegisterModelRoutes(v1, cmdbController)
			RegisterModelAttrGroupRoutes(v1, cmdbController)
			RegisterModelAttrRoutes(v1, cmdbController)
			RegisterModelDataRoutes(v1, cmdbController)
			RegisterModelRelationRoutes(v1, cmdbController)
			RegisterDataRelationRoutes(v1, cmdbController)
			RegisterDataViewRoutes(v1, cmdbController)
			RegisterLabelRoutes(v1, cmdbController)

			// 获取审计记录
			v1.POST("/audit_logs", cmdbController.GetAuditRecordList)
			v1.POST("/audit_logs_by_id", cmdbController.GetAuditRecordDetail)
			// 用户搜索
			v1.GET("/users_search", cmdbController.GetUsersSearch)
		}

		v2 := api.Group("/v2")

		// 初始化审计中间件
		// v2.Use(audit.CmdbAuditLog(db))
		{
			// 初始化cmdb的控制器
			cmdbController := cmdb.NewCMDBController(db)
			cmdbV2 := v2.Group("/cmdb")
			cmdbV2.Use(func(c *gin.Context) {
				// 判断是否是通过AMC调用的接口，该标识为前后端约定后的固定标识
				amc := c.Request.Header.Get("SVC-AMC")
				if amc == "true" {
					zap.L().Debug("使用权限中台提供的SSO认证")
					userName := c.Request.Header.Get("Username")
					displayName := c.Request.Header.Get("Displayname")
					avatar := c.Request.Header.Get("Avatar")
					c.Set("username", userName)
					c.Set("display_name", displayName)
					c.Set("avatar", avatar)

					zap.L().Debug("amc认证信息",
						zap.String("username", userName),
						zap.String("display_name", displayName),
						zap.String("avatar", avatar),
					)
				} else {
					zap.L().Debug("使用JWT Auth Token")
					jwtStrategy, _ := newJwtAuth().(auth.JWTStrategy)
					jwtStrategy.AuthFunc()(c)
				}

				c.Next()
			})

			{
				RegisterModelGroupRoutes(cmdbV2, cmdbController)
				RegisterModelRoutes(cmdbV2, cmdbController)
				RegisterModelAttrGroupRoutes(cmdbV2, cmdbController)
				RegisterModelAttrRoutes(cmdbV2, cmdbController)
				RegisterModelDataRoutes(cmdbV2, cmdbController)
				RegisterModelRelationRoutes(cmdbV2, cmdbController)
				RegisterDataRelationRoutes(cmdbV2, cmdbController)
				RegisterDataViewRoutes(cmdbV2, cmdbController)
				RegisterLabelRoutes(cmdbV2, cmdbController)

				// 获取审计记录
				cmdbV2.POST("/audit_logs", cmdbController.GetAuditRecordList)
				cmdbV2.POST("/audit_logs_by_id", cmdbController.GetAuditRecordDetail)
				// 用户搜索
				cmdbV2.GET("/users_search", cmdbController.GetUsersSearch)
			}

			biz := v2.Group("/biz")
			{
				RegisterBizInfoRoutes(biz, cmdbController)
				biz.GET("/data_view/physical/rack_spare", cmdbController.RackSpare)
			}

			agent := v2.Group("/agent")
			{
				RegisterReportRoutes(agent, cmdbController)
			}
		}
	}

	// 定义服务的健康检查接口
	c := g.Group("/check")
	{
		c.GET("/health", check.HealthCheck)
	}

	// 定义openapi的路由
	openapi := g.Group("/cmdb-openapi")
	openapi.Use(commonMiddleware.AuthOpenApiToken())
	{
		api := openapi.Group("/api")
		{
			v1 := api.Group("/v1")
			{
				openAPICmdbController := cmdb.NewCMDBController(db)
				RegisterBizInfoRoutes(v1, openAPICmdbController)
				RegisterModelDataRoutes(v1, openAPICmdbController)
			}
		}
	}

	return g
}
