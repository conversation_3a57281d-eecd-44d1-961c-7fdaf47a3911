package v1

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/utils"
)

/*
数据验证器设计模式重构Demo

本文件展示了如何使用设计模式重构原有的model_data_validate.go中的验证逻辑。

采用的设计模式：
1. 策略模式 (Strategy Pattern): 将不同的数据类型验证逻辑封装成独立的策略类
2. 责任链模式 (Chain of Responsibility): 将验证器串联成链，每个验证器负责特定的验证逻辑
3. 工厂模式 (Factory Pattern): 用于创建不同类型的验证策略
4. 建造者模式 (Builder Pattern): 用于构建复杂的验证器链

优势：
- 单一职责：每个验证器只负责一种验证逻辑
- 开闭原则：新增验证规则时不需要修改现有代码
- 可测试性：每个验证器可以独立测试
- 可维护性：验证逻辑清晰，易于理解和维护
- 可复用性：验证器可以在不同场景下复用

验证器链的执行顺序：
1. BasicDataValidator - 基础数据验证
2. FieldExistenceValidator - 字段存在性验证
3. DataTypeValidator - 数据类型验证
4. RegexValidator - 正则表达式验证
5. RequiredFieldValidator - 必填字段验证
6. UniquenessValidator - 唯一性验证
7. EnumValidator - 枚举值验证
8. EditabilityValidator - 可编辑性验证（仅更新操作）
*/

// ValidatorContext 验证上下文，包含验证所需的所有信息
type ValidatorContext struct {
	Ctx         context.Context
	Data        *v1.ModelData
	Action      string // "create" or "update"
	ModelAttrs  []v1.CommonModelAttribute
	AttrCodeMap map[string]v1.CommonModelAttribute
	Store       interface{} // 数据存储接口，这里简化处理
}

// ValidationResult 验证结果
type ValidationResult struct {
	IsValid bool
	Error   error
}

// Validator 验证器接口
type Validator interface {
	Validate(ctx *ValidatorContext) *ValidationResult
	SetNext(validator Validator) Validator
}

// BaseValidator 基础验证器，实现责任链模式
type BaseValidator struct {
	next Validator
}

func (v *BaseValidator) SetNext(validator Validator) Validator {
	v.next = validator
	return validator
}

func (v *BaseValidator) executeNext(ctx *ValidatorContext) *ValidationResult {
	if v.next != nil {
		return v.next.Validate(ctx)
	}
	return &ValidationResult{IsValid: true, Error: nil}
}

// 1. 基础数据验证器
type BasicDataValidator struct {
	BaseValidator
}

func (v *BasicDataValidator) Validate(ctx *ValidatorContext) *ValidationResult {
	// 检查data是否为空
	if len(ctx.Data.Data) == 0 {
		return &ValidationResult{
			IsValid: false,
			Error:   errno.ErrParameterRequired.Add("请传递data数据"),
		}
	}

	// 检查更新操作是否有ID
	if ctx.Action == "update" && ctx.Data.ID == "" {
		return &ValidationResult{
			IsValid: false,
			Error:   errno.ErrParameterRequired.Add("更新操作必须传递id字段"),
		}
	}

	// 检查identify_value与code字段一致性
	dataCode, ok := ctx.Data.Data[ctx.Data.ModelCode+"_code"]
	if !ok {
		return &ValidationResult{
			IsValid: false,
			Error:   errno.InternalServerError.Addf("data中找不到code为%s的字段", ctx.Data.ModelCode+"_code"),
		}
	}
	if ctx.Data.IdentifyValue != dataCode {
		return &ValidationResult{
			IsValid: false,
			Error:   errno.ErrParameterInvalid.Addf("identify_value必须与data中的%s字段保持一致", dataCode),
		}
	}

	return v.executeNext(ctx)
}

// 2. 字段存在性验证器
type FieldExistenceValidator struct {
	BaseValidator
}

func (v *FieldExistenceValidator) Validate(ctx *ValidatorContext) *ValidationResult {
	userAttrCodeSlice := ctx.Data.Data.Keys()
	attrCodeSlice := make([]string, 0, len(ctx.ModelAttrs))
	for _, attr := range ctx.ModelAttrs {
		attrCodeSlice = append(attrCodeSlice, attr.Code)
	}

	// 检查用户提交的字段是否都存在于模型中
	for _, userPostAttrCode := range userAttrCodeSlice {
		if !utils.FindStrInStringSlice(attrCodeSlice, userPostAttrCode) {
			return &ValidationResult{
				IsValid: false,
				Error:   errno.ErrDataInvalid.Addf("用户提交的模型属性code %s 不存在", userPostAttrCode),
			}
		}
	}

	return v.executeNext(ctx)
}

// 3. 数据类型验证策略接口
type TypeValidationStrategy interface {
	ValidateType(fieldCode string, value interface{}, attr v1.CommonModelAttribute) error
}

// 整数类型验证策略
type IntTypeStrategy struct{}

func (s *IntTypeStrategy) ValidateType(fieldCode string, value interface{}, attr v1.CommonModelAttribute) error {
	valueStr := strings.TrimSpace(utils.ToString(value))
	if valueStr != "" {
		if utils.ToInt(value) == 0 && valueStr != "0" {
			sample := strings.TrimSpace(utils.ToString(attr.Attrs["sample"]))
			if sample != "" {
				sample = fmt.Sprintf("如：%s", sample)
			}
			return errno.ErrDataInvalid.Addf("用户提交的模型属性 [%s] 的值类型不合法，期望整数类型 %s", attr.Name, sample)
		}
	}
	return nil
}

// 浮点数类型验证策略
type FloatTypeStrategy struct{}

func (s *FloatTypeStrategy) ValidateType(fieldCode string, value interface{}, attr v1.CommonModelAttribute) error {
	valueStr := strings.TrimSpace(utils.ToString(value))
	if valueStr != "" {
		if utils.ToFloat64(value) == 0 && valueStr != "0" {
			sample := strings.TrimSpace(utils.ToString(attr.Attrs["sample"]))
			if sample != "" {
				sample = fmt.Sprintf("如：%s", sample)
			}
			return errno.ErrDataInvalid.Addf("用户提交的模型属性 [%s] 的值类型不合法，期望浮点数类型 %s", attr.Name, sample)
		}
	}
	return nil
}

// 布尔类型验证策略
type BoolTypeStrategy struct{}

func (s *BoolTypeStrategy) ValidateType(fieldCode string, value interface{}, attr v1.CommonModelAttribute) error {
	valueStr := strings.TrimSpace(utils.ToString(value))
	if valueStr != "" {
		boolValue := strings.ToLower(valueStr)
		if boolValue != "true" && boolValue != "false" {
			sample := strings.TrimSpace(utils.ToString(attr.Attrs["sample"]))
			if sample != "" {
				sample = fmt.Sprintf("如：%s", sample)
			}
			return errno.ErrDataInvalid.Addf("用户提交的模型属性 [%s] 的值类型不合法，期望布尔类型 %s", attr.Name, sample)
		}
	}
	return nil
}

// 日期类型验证策略
type DateTypeStrategy struct{}

func (s *DateTypeStrategy) ValidateType(fieldCode string, value interface{}, attr v1.CommonModelAttribute) error {
	valueStr := strings.TrimSpace(utils.ToString(value))
	if valueStr != "" {
		_, err1 := time.Parse("2006-01-02", valueStr)
		_, err2 := time.Parse("2006/01/02", valueStr)
		if err1 != nil && err2 != nil {
			sample := strings.TrimSpace(utils.ToString(attr.Attrs["sample"]))
			if sample != "" {
				sample = fmt.Sprintf("如：%s", sample)
			}
			return errno.ErrDataInvalid.Addf("用户提交的模型属性 [%s] 的值类型不合法，期望日期类型 %s", attr.Name, sample)
		}
		// 这里可以统一格式化日期格式
		// 注意：在实际实现中，如果需要修改原始数据格式，需要传入数据的引用
	}
	return nil
}

// 字符串类型验证策略
type StringTypeStrategy struct{}

func (s *StringTypeStrategy) ValidateType(fieldCode string, value interface{}, attr v1.CommonModelAttribute) error {
	if value != nil {
		v := utils.ToString(value)
		if len(v) > 512 {
			return errno.ErrDataInvalid.Addf("用户提交的模型属性code %s 的长度超过了最大长度512", fieldCode)
		}
	}
	return nil
}

// 类型验证策略工厂
type TypeValidationStrategyFactory struct {
	strategies map[string]TypeValidationStrategy
}

func NewTypeValidationStrategyFactory() *TypeValidationStrategyFactory {
	return &TypeValidationStrategyFactory{
		strategies: map[string]TypeValidationStrategy{
			"int":    &IntTypeStrategy{},
			"float":  &FloatTypeStrategy{},
			"bool":   &BoolTypeStrategy{},
			"date":   &DateTypeStrategy{},
			"string": &StringTypeStrategy{},
		},
	}
}

func (f *TypeValidationStrategyFactory) GetStrategy(typeName string) TypeValidationStrategy {
	if strategy, exists := f.strategies[typeName]; exists {
		return strategy
	}
	return &StringTypeStrategy{} // 默认策略
}

// 4. 数据类型验证器
type DataTypeValidator struct {
	BaseValidator
	strategyFactory *TypeValidationStrategyFactory
}

func NewDataTypeValidator() *DataTypeValidator {
	return &DataTypeValidator{
		strategyFactory: NewTypeValidationStrategyFactory(),
	}
}

func (v *DataTypeValidator) Validate(ctx *ValidatorContext) *ValidationResult {
	userAttrCodeSlice := ctx.Data.Data.Keys()

	for _, userPostAttrCode := range userAttrCodeSlice {
		modelAttrObj := ctx.AttrCodeMap[userPostAttrCode]
		userPostAttrValue := ctx.Data.Data[userPostAttrCode]

		// 使用策略模式进行类型验证
		strategy := v.strategyFactory.GetStrategy(modelAttrObj.TypeName)
		if err := strategy.ValidateType(userPostAttrCode, userPostAttrValue, modelAttrObj); err != nil {
			return &ValidationResult{
				IsValid: false,
				Error:   err,
			}
		}
	}

	return v.executeNext(ctx)
}

// 5. 正则表达式验证器
type RegexValidator struct {
	BaseValidator
	checkSystemField bool
}

func NewRegexValidator(checkSystemField bool) *RegexValidator {
	return &RegexValidator{
		checkSystemField: checkSystemField,
	}
}

func (v *RegexValidator) Validate(ctx *ValidatorContext) *ValidationResult {
	userAttrCodeSlice := ctx.Data.Data.Keys()
	modelCode := ctx.Data.ModelCode

	for _, userPostAttrCode := range userAttrCodeSlice {
		modelAttrObj := ctx.AttrCodeMap[userPostAttrCode]

		// 检查是否需要验证系统字段
		if modelAttrObj.AttrGroup != fmt.Sprintf("%s_system", modelCode) || v.checkSystemField {
			if err := v.validateRegexRule(userPostAttrCode, ctx.Data.Data[userPostAttrCode], modelAttrObj); err != nil {
				return &ValidationResult{
					IsValid: false,
					Error:   err,
				}
			}
		}
	}

	return v.executeNext(ctx)
}

func (v *RegexValidator) validateRegexRule(fieldCode string, value interface{}, attr v1.CommonModelAttribute) error {
	rule, ok := attr.Attrs["rule"].(map[string]interface{})
	if !ok {
		return nil
	}

	ruleRe, ok := rule["rule_re"].(string)
	if !ok || ruleRe == "" {
		return nil
	}

	userPostData := utils.ToString(value)
	if userPostData == "" || attr.TypeName == "bool" {
		return nil
	}

	var re *regexp.Regexp

	if attr.TypeName == "string" || attr.TypeName == "textarea" {
		pattern, err := strconv.Unquote(`"` + ruleRe + `"`)
		if err != nil {
			pattern = ruleRe
		}
		re = regexp.MustCompile(pattern)
	} else {
		re = regexp.MustCompile(ruleRe)
	}

	if !re.MatchString(userPostData) {
		errMsg := ""
		if r := v1.GetRegexName(ruleRe); r != "" {
			errMsg = fmt.Sprintf("用户提交的模型属性字段[%s] 不符合正则规则: [%s]; 用户提交的值为 '%s'", attr.Name, r, userPostData)
		} else {
			sampleData, ok := attr.Attrs["sample"].(string)
			if ok {
				errMsg = fmt.Sprintf("用户提交的模型属性字段[%s] 不符合正则规则，示例数据为：%s; 用户提交的值为 '%s'", attr.Name, sampleData, userPostData)
			} else {
				errMsg = fmt.Sprintf("用户提交的模型属性字段[%s] 不符合正则规则，用户提交的值为 '%s', 正则规则为 '%s'", attr.Name, userPostData, ruleRe)
			}
		}
		return errno.ErrDataInvalid.Add(errMsg)
	}

	return nil
}

// 6. 必填字段验证器
type RequiredFieldValidator struct {
	BaseValidator
	checkSystemField bool
}

func NewRequiredFieldValidator(checkSystemField bool) *RequiredFieldValidator {
	return &RequiredFieldValidator{
		checkSystemField: checkSystemField,
	}
}

func (v *RequiredFieldValidator) Validate(ctx *ValidatorContext) *ValidationResult {
	userAttrCodeSlice := ctx.Data.Data.Keys()
	modelCode := ctx.Data.ModelCode

	// 获取必填字段列表
	requiredFields := v.getRequiredFields(ctx.ModelAttrs)

	for _, requiredField := range requiredFields {
		currentAttrGroup := ctx.AttrCodeMap[requiredField].AttrGroup

		// 过滤系统字段
		if currentAttrGroup != fmt.Sprintf("%s_system", modelCode) || v.checkSystemField {
			// 检查字段是否存在
			if !utils.FindStrInStringSlice(userAttrCodeSlice, requiredField) {
				return &ValidationResult{
					IsValid: false,
					Error:   errno.ErrParameterRequired.Addf("必填字段 %s 没有传递", ctx.AttrCodeMap[requiredField].Name),
				}
			}

			// 检查字段值是否为空
			if ctx.Data.Data[requiredField] == "" || ctx.Data.Data[requiredField] == nil {
				// 特殊处理：必填但不可编辑的字段在更新时可以为空
				unEditableFields := v.getUnEditableFields(ctx.ModelAttrs)
				isUnEditable := utils.FindStrInStringSlice(unEditableFields, requiredField)
				if !isUnEditable || (isUnEditable && ctx.Action == "create") {
					return &ValidationResult{
						IsValid: false,
						Error:   errno.ErrParameterRequired.Addf("必填字段 %s 不能为空", ctx.AttrCodeMap[requiredField].Name),
					}
				}
			}
		}
	}

	return v.executeNext(ctx)
}

func (v *RequiredFieldValidator) getRequiredFields(modelAttrs []v1.CommonModelAttribute) []string {
	var requiredFields []string
	for _, attr := range modelAttrs {
		if !attr.IsNull && attr.TypeName != "relationship" {
			requiredFields = append(requiredFields, attr.Code)
		}
	}
	return requiredFields
}

func (v *RequiredFieldValidator) getUnEditableFields(modelAttrs []v1.CommonModelAttribute) []string {
	var unEditableFields []string
	for _, attr := range modelAttrs {
		if !attr.IsEditAble && attr.TypeName != "relationship" {
			unEditableFields = append(unEditableFields, attr.Code)
		}
	}
	return unEditableFields
}

// 7. 唯一性验证器
type UniquenessValidator struct {
	BaseValidator
	checkSystemField bool
	store            interface{} // 这里简化处理，实际应该是具体的存储接口
}

func NewUniquenessValidator(checkSystemField bool, store interface{}) *UniquenessValidator {
	return &UniquenessValidator{
		checkSystemField: checkSystemField,
		store:            store,
	}
}

func (v *UniquenessValidator) Validate(ctx *ValidatorContext) *ValidationResult {
	modelCode := ctx.Data.ModelCode
	uniqueFields := v.getUniqueFields(ctx.ModelAttrs)

	for _, uniqueField := range uniqueFields {
		modelAttrObj := ctx.AttrCodeMap[uniqueField]
		if modelAttrObj.AttrGroup != fmt.Sprintf("%s_system", modelCode) || v.checkSystemField {
			if err := v.validateUniqueness(ctx, uniqueField); err != nil {
				return &ValidationResult{
					IsValid: false,
					Error:   err,
				}
			}
		}
	}

	return v.executeNext(ctx)
}

func (v *UniquenessValidator) getUniqueFields(modelAttrs []v1.CommonModelAttribute) []string {
	var uniqueFields []string
	for _, attr := range modelAttrs {
		if attr.IsUnique && attr.TypeName != "relationship" {
			uniqueFields = append(uniqueFields, attr.Code)
		}
	}
	return uniqueFields
}

func (v *UniquenessValidator) validateUniqueness(ctx *ValidatorContext, uniqueField string) error {
	// 这里简化实现，实际需要调用数据库查询
	// 在真实实现中，这里应该调用 store 接口进行数据库查询

	dataValue := ""
	fieldValue, exist := ctx.Data.Data[uniqueField]
	if exist && fieldValue != nil {
		dataValue = utils.ToString(fieldValue)
	}

	// 空值不做唯一性校验
	if dataValue == "" {
		return nil
	}

	// 这里应该实现实际的数据库查询逻辑
	// 示例：
	// filter := bson.M{
	//     "model_code": ctx.Data.ModelCode,
	//     "data." + uniqueField: dataValue,
	// }
	// dList, err := v.store.GetModelDataByCustomFilter(ctx.Ctx, filter, nil)
	//
	// 根据 action 类型进行不同的处理
	// if ctx.Action == "create" && len(dList) > 0 {
	//     return errno.ErrDataCheck.Addf("模型字段 %s 的值 %s 已经存在，不能重复",
	//         ctx.AttrCodeMap[uniqueField].Name, dataValue)
	// }
	// if ctx.Action == "update" && len(dList) == 1 && dList[0].ID != ctx.Data.ID {
	//     return errno.ErrDataCheck.Addf("模型字段 %s 的值 %s 已经存在，不能重复",
	//         ctx.AttrCodeMap[uniqueField].Name, dataValue)
	// }

	return nil
}

// 8. 枚举值验证器
type EnumValidator struct {
	BaseValidator
}

func NewEnumValidator() *EnumValidator {
	return &EnumValidator{}
}

func (v *EnumValidator) Validate(ctx *ValidatorContext) *ValidationResult {
	generalEnumFields := v.getGeneralEnumFields(ctx.ModelAttrs)
	subEnumFields := v.getSubEnumFields(ctx.ModelAttrs)

	// 验证普通枚举字段
	for _, enumField := range generalEnumFields {
		if err := v.validateGeneralEnum(ctx, enumField); err != nil {
			return &ValidationResult{
				IsValid: false,
				Error:   err,
			}
		}
	}

	// 验证继承枚举字段
	for _, enumField := range subEnumFields {
		if err := v.validateSubEnum(ctx, enumField); err != nil {
			return &ValidationResult{
				IsValid: false,
				Error:   err,
			}
		}
	}

	return v.executeNext(ctx)
}

func (v *EnumValidator) getGeneralEnumFields(modelAttrs []v1.CommonModelAttribute) []string {
	var fields []string
	for _, attr := range modelAttrs {
		if attr.TypeName == "select" {
			if inherit, ok := attr.Attrs["inherit"]; ok {
				if isInherit, ok := inherit.(bool); !ok || !isInherit {
					fields = append(fields, attr.Code)
				}
			} else {
				fields = append(fields, attr.Code)
			}
		}
	}
	return fields
}

func (v *EnumValidator) getSubEnumFields(modelAttrs []v1.CommonModelAttribute) []string {
	var fields []string
	for _, attr := range modelAttrs {
		if attr.TypeName == "select" {
			if inherit, ok := attr.Attrs["inherit"]; ok {
				if isInherit, ok := inherit.(bool); ok && isInherit {
					fields = append(fields, attr.Code)
				}
			}
		}
	}
	return fields
}

func (v *EnumValidator) validateGeneralEnum(ctx *ValidatorContext, enumField string) error {
	field := ctx.AttrCodeMap[enumField]

	// 不可编辑字段在更新时跳过验证
	if !field.IsEditAble && ctx.Action == "update" {
		return nil
	}

	fieldData, ok := ctx.Data.Data[enumField].(string)
	if fieldData == "" {
		return nil
	}

	if ok {
		// 这里简化处理，实际需要根据具体的数据结构来验证
		// opts := field.Attrs["opts"].(primitive.A)
		// 验证枚举值是否存在于选项中
		// 实际实现需要遍历 opts 进行匹配
	}

	return nil
}

func (v *EnumValidator) validateSubEnum(ctx *ValidatorContext, enumField string) error {
	// 这里简化处理继承枚举的验证逻辑
	// 实际实现需要检查子枚举与父枚举的继承关系
	return nil
}

// 9. 可编辑性验证器（仅用于更新操作）
type EditabilityValidator struct {
	BaseValidator
	store interface{} // 数据存储接口
}

func NewEditabilityValidator(store interface{}) *EditabilityValidator {
	return &EditabilityValidator{
		store: store,
	}
}

func (v *EditabilityValidator) Validate(ctx *ValidatorContext) *ValidationResult {
	// 只有更新操作才需要验证可编辑性
	if ctx.Action != "update" {
		return v.executeNext(ctx)
	}

	unEditableFields := v.getUnEditableFields(ctx.ModelAttrs)

	// 这里需要获取旧数据进行比较
	// oldData, err := v.store.GetModelDataByID(ctx.Ctx, ctx.Data.ID)
	// if err != nil {
	//     return &ValidationResult{
	//         IsValid: false,
	//         Error:   errno.ErrDataNotExists.Addf("模型数据id %s 不存在", ctx.Data.ID),
	//     }
	// }

	for _, unEditableField := range unEditableFields {
		_ = unEditableField // 避免未使用变量警告
		// 简化处理，实际需要比较新旧数据
		// oldValue, ok := oldData.Data[unEditableField]
		// if !ok {
		//     oldValue = ""
		// }
		// newValue, ok := ctx.Data.Data[unEditableField]
		// if !ok {
		//     continue
		// }
		// if oldValue != newValue {
		//     return &ValidationResult{
		//         IsValid: false,
		//         Error:   errno.ErrDataFieldUnEditable.Addf("不可编辑字段 %s 不能更新",
		//             ctx.AttrCodeMap[unEditableField].Name),
		//     }
		// }
	}

	return v.executeNext(ctx)
}

func (v *EditabilityValidator) getUnEditableFields(modelAttrs []v1.CommonModelAttribute) []string {
	var unEditableFields []string
	for _, attr := range modelAttrs {
		if !attr.IsEditAble && attr.TypeName != "relationship" {
			unEditableFields = append(unEditableFields, attr.Code)
		}
	}
	return unEditableFields
}

// 验证器链构建器
type ValidatorChainBuilder struct {
	checkSystemField bool
	store            interface{}
}

func NewValidatorChainBuilder(checkSystemField bool, store interface{}) *ValidatorChainBuilder {
	return &ValidatorChainBuilder{
		checkSystemField: checkSystemField,
		store:            store,
	}
}

// 构建验证器链
func (b *ValidatorChainBuilder) BuildChain() Validator {
	// 创建各个验证器
	basicValidator := &BasicDataValidator{}
	fieldExistenceValidator := &FieldExistenceValidator{}
	dataTypeValidator := NewDataTypeValidator()
	regexValidator := NewRegexValidator(b.checkSystemField)
	requiredFieldValidator := NewRequiredFieldValidator(b.checkSystemField)
	uniquenessValidator := NewUniquenessValidator(b.checkSystemField, b.store)
	enumValidator := NewEnumValidator()
	editabilityValidator := NewEditabilityValidator(b.store)

	// 构建责任链
	basicValidator.
		SetNext(fieldExistenceValidator).
		SetNext(dataTypeValidator).
		SetNext(regexValidator).
		SetNext(requiredFieldValidator).
		SetNext(uniquenessValidator).
		SetNext(enumValidator).
		SetNext(editabilityValidator)

	return basicValidator
}

// 数据验证服务
type DataValidationService struct {
	validatorChain Validator
}

func NewDataValidationService(checkSystemField bool, store interface{}) *DataValidationService {
	builder := NewValidatorChainBuilder(checkSystemField, store)
	return &DataValidationService{
		validatorChain: builder.BuildChain(),
	}
}

// 执行验证
func (s *DataValidationService) ValidateData(ctx context.Context, data *v1.ModelData, action string, modelAttrs []v1.CommonModelAttribute) error {
	// 构建属性映射
	attrCodeMap := make(map[string]v1.CommonModelAttribute)
	for _, attr := range modelAttrs {
		attrCodeMap[attr.Code] = attr
	}

	// 创建验证上下文
	validationCtx := &ValidatorContext{
		Ctx:         ctx,
		Data:        data,
		Action:      action,
		ModelAttrs:  modelAttrs,
		AttrCodeMap: attrCodeMap,
	}

	// 执行验证链
	result := s.validatorChain.Validate(validationCtx)
	if !result.IsValid {
		return result.Error
	}

	return nil
}

// 使用示例函数
func ExampleUsage(ctx context.Context, data *v1.ModelData, action string, modelAttrs []v1.CommonModelAttribute, store interface{}) error {
	// 创建验证服务
	validationService := NewDataValidationService(false, store)

	// 执行验证
	if err := validationService.ValidateData(ctx, data, action, modelAttrs); err != nil {
		return err
	}

	// 验证通过，可以继续后续业务逻辑
	return nil
}

/*
重构总结：

原有代码问题：
1. 单一方法过长（500+行），职责不清晰
2. 多种验证逻辑混杂在一起，难以维护
3. 硬编码的验证规则，扩展性差
4. 测试困难，无法单独测试某种验证逻辑

重构后的优势：
1. 职责分离：每个验证器只负责一种验证逻辑
2. 易于扩展：新增验证规则只需实现新的验证器
3. 易于测试：每个验证器可以独立进行单元测试
4. 配置灵活：可以根据需要动态组装验证器链
5. 代码复用：验证器可以在不同场景下复用

如何集成到现有代码：
1. 在原有的dataValidCheck方法中，替换为调用DataValidationService
2. 逐步迁移现有的验证逻辑到对应的验证器中
3. 完善各个验证器的实现，特别是数据库查询相关的逻辑
4. 添加完整的单元测试覆盖

扩展建议：
1. 可以考虑添加验证器的优先级和条件执行
2. 支持异步验证（如需要调用外部服务的验证）
3. 添加验证结果的缓存机制
4. 支持验证规则的动态配置
*/
