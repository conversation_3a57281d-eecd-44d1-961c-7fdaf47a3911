package rack_batch

import (
	"encoding/json"
	"fmt"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

const RackPlaceHolder = "rack_placeholder"

// findDevice 通过序列号查找设备
func (p *RackBatchProcessor) findDevice(serialNumber string) (*DeviceInfo, error) {
	// 使用精确匹配查找设备
	filter := bson.M{"meta_data.universal_sn": serialNumber}

	dataList, err := p.store.ModelData().GetModelDataByCustomFilter(p.ctx, filter, nil)
	if err != nil {
		return nil, fmt.Errorf("查询设备失败: %v", err)
	}

	if len(dataList) == 0 {
		return nil, fmt.Errorf("未找到序列号为 %s 的设备", serialNumber)
	}

	if len(dataList) > 1 {
		return nil, fmt.Errorf("序列号 %s 对应多台设备，数据不唯一", serialNumber)
	}

	data := dataList[0]

	// 获取模型映射
	modelMapping, err := p.store.Model().GetModelMapping(p.ctx)
	if err != nil {
		return nil, fmt.Errorf("获取模型映射失败: %v", err)
	}

	modelName := ""
	if model, exists := modelMapping[data.ModelCode]; exists {
		modelName = model.Name
	}

	// 获取当前机柜信息
	currentRack := ""
	currentStartU := 0
	currentHeight := 0

	if data.ParentID != "" {
		// 设备已上架，获取当前位置信息
		rackData, err := p.store.ModelData().GetModelDataByID(p.ctx, data.ParentID)
		if err == nil && rackData != nil {
			currentRack = rackData.Name()
		}

		// 获取当前U位信息
		if startU, exists := data.Data[fmt.Sprintf("%s_start_u", data.ModelCode)]; exists {
			currentStartU = utils.ToInt(startU)
		}
		if height, exists := data.Data[fmt.Sprintf("%s_height", data.ModelCode)]; exists {
			currentHeight = utils.ToInt(height)
		}
	}

	// 获取负责人信息
	owner := ""
	if ownerField, exists := data.Data[fmt.Sprintf("%s_owner", data.ModelCode)]; exists {
		owner = utils.ToString(ownerField)
	}

	return &DeviceInfo{
		ID:            data.ID,
		SerialNumber:  serialNumber,
		Name:          data.Name(),
		ModelCode:     data.ModelCode,
		ModelName:     modelName,
		CurrentRack:   currentRack,
		CurrentStartU: currentStartU,
		CurrentHeight: currentHeight,
		Owner:         owner,
	}, nil
}

// findRack 通过机柜名称查找机柜
func (p *RackBatchProcessor) findRack(rackName string) (*RackInfo, error) {
	// 查找机柜，使用精确匹配
	filter := bson.M{
		"model_code":     "rack",
		"data.rack_name": rackName,
	}

	dataList, err := p.store.ModelData().GetModelDataByCustomFilter(p.ctx, filter, nil)
	if err != nil {
		return nil, fmt.Errorf("查询机柜失败: %v", err)
	}

	if len(dataList) == 0 {
		return nil, fmt.Errorf("未找到名称为 %s 的机柜", rackName)
	}

	if len(dataList) > 1 {
		return nil, fmt.Errorf("机柜名称 %s 对应多个机柜，数据不唯一", rackName)
	}

	data := dataList[0]

	// 转换为机柜对象
	rack, err := v1.ToRackData(*data)
	if err != nil {
		return nil, fmt.Errorf("数据转换失败: %v", err)
	}

	// 获取机房信息
	idcName := ""
	location := ""
	if data.ParentID != "" {
		idcData, err := p.store.ModelData().GetModelDataByID(p.ctx, data.ParentID)
		if err == nil && idcData != nil {
			idcName = idcData.Name()

			// 获取位置信息
			if idcData.ParentID != "" {
				officeData, err := p.store.ModelData().GetModelDataByID(p.ctx, idcData.ParentID)
				if err == nil && officeData != nil {
					location = officeData.Name()
				}
			}
		}
	}

	return &RackInfo{
		ID:       data.ID,
		Name:     rackName,
		Height:   rack.Height(),
		IDCName:  idcName,
		Location: location,
	}, nil
}

// checkUPositionConflict 检查U位冲突（复用现有service层逻辑）
func (p *RackBatchProcessor) checkUPositionConflict(rackID string, startU, height int, excludeDeviceID string) (*ConflictRecord, error) {
	// 获取机柜数据
	rackData, err := p.store.ModelData().GetModelDataByID(p.ctx, rackID)
	if err != nil {
		return nil, fmt.Errorf("获取机柜数据失败: %v", err)
	}

	// 复用现有service层的逻辑进行冲突检测
	// 1. 获取机柜上的所有设备
	filter := bson.M{"parent_id": rackID}
	if excludeDeviceID != "" {
		filter["_id"] = bson.M{"$ne": excludeDeviceID}
	}

	dataList, err := p.store.ModelData().GetModelDataByCustomFilter(p.ctx, filter, nil)
	if err != nil {
		return nil, fmt.Errorf("查询机柜设备失败: %v", err)
	}

	// 2. 获取占位符信息
	phList := make([]v1.PlaceHolder, 0)
	if phs, ok := rackData.Data.Get("rack_placeholder"); ok && phs != "" {
		if err := json.Unmarshal([]byte(utils.ToString(phs)), &phList); err != nil {
			return nil, fmt.Errorf("解析占位符失败: %v", err)
		}
	}

	// 3. 检查与现有设备的冲突
	requestedRange := [2]int{startU, startU + height - 1}

	// 检查设备冲突
	for _, data := range dataList {
		device, err := v1.ToDev(*data)
		if err != nil {
			continue
		}

		deviceRange, valid := device.Range()
		if !valid {
			continue
		}

		if p.rangesOverlap(requestedRange, deviceRange) {
			// 找到冲突设备
			sn := ""
			if snField, exists := data.MetaData["universal_sn"]; exists {
				sn = utils.ToString(snField)
			}
			if sn == "" {
				sn = data.Name()
			}

			return &ConflictRecord{
				ConflictDevice: sn,
				ConflictRange:  fmt.Sprintf("%d-%d", deviceRange[0], deviceRange[1]),
				ConflictType:   "device",
			}, nil
		}
	}

	// 检查占位符冲突
	for _, ph := range phList {
		phRange, valid := ph.Range()
		if !valid {
			continue
		}

		if p.rangesOverlap(requestedRange, phRange) {
			return &ConflictRecord{
				ConflictDevice: "占位符",
				ConflictRange:  fmt.Sprintf("%d-%d", phRange[0], phRange[1]),
				ConflictType:   "placeholder",
			}, nil
		}
	}

	return nil, nil // 无冲突
}

// rangesOverlap 检查两个范围是否重叠
func (p *RackBatchProcessor) rangesOverlap(range1, range2 [2]int) bool {
	return range1[0] <= range2[1] && range2[0] <= range1[1]
}

// updateDevicePosition 更新设备位置
func (p *RackBatchProcessor) updateDevicePosition(deviceInfo *DeviceInfo, rackInfo *RackInfo, record *RackPositionRecord) (string, error) {
	// 准备更新数据
	updateData := &v1.ModelData{
		ID:        deviceInfo.ID,
		ModelCode: deviceInfo.ModelCode,
		Data:      make(mapdata.MapData),
	}

	// 设置U位信息
	updateData.Data[fmt.Sprintf("%s_start_u", deviceInfo.ModelCode)] = record.StartU
	updateData.Data[fmt.Sprintf("%s_height", deviceInfo.ModelCode)] = record.Height

	// 设置负责人信息（如果提供）
	if record.Owner != "" {
		updateData.Data[fmt.Sprintf("%s_owner", deviceInfo.ModelCode)] = record.Owner
	}

	// 判断操作类型
	action := "create"
	updateParent := false

	if deviceInfo.CurrentRack == "" {
		// 设备未上架，需要设置父机柜
		updateParent = true
		action = "create"
	} else if deviceInfo.CurrentRack != rackInfo.Name {
		// 设备需要移动到新机柜
		updateParent = true
		action = "move"
	} else {
		// 设备在同一机柜内调整位置
		action = "update"
	}

	// 执行更新
	updateRequest := &v1.UpdateModelDataStoreRequest{
		Id:           deviceInfo.ID,
		ModelData:    updateData,
		UpdateParent: updateParent,
	}

	if updateParent {
		updateRequest.ParentID = rackInfo.ID
		updateRequest.ParentDesc = rackInfo.Name
	}

	// 执行更新 - 检查是否为试运行模式
	if !p.dryRun {
		// 实际更新数据库
		if _, err := p.store.ModelData().UpdateModelData(p.ctx, updateRequest); err != nil {
			return "", fmt.Errorf("更新设备数据失败: %v", err)
		}
	}

	// 统一的审计日志记录
	zap.L().Info("设备位置更新",
		zap.String("device_id", deviceInfo.ID),
		zap.String("device_name", deviceInfo.Name),
		zap.String("action", action),
		zap.String("rack_change", fmt.Sprintf("%s -> %s", deviceInfo.CurrentRack, rackInfo.Name)),
		zap.String("position_change", fmt.Sprintf("U%d-%d -> U%d-%d",
			deviceInfo.CurrentStartU, deviceInfo.CurrentStartU+deviceInfo.CurrentHeight-1,
			record.StartU, record.StartU+record.Height-1)),
		zap.String("owner_change", fmt.Sprintf("%s -> %s", deviceInfo.Owner, record.Owner)))

	return action, nil
}
