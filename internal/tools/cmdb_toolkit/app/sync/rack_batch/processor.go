package rack_batch

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/db"

	"github.com/fatih/color"
	"go.uber.org/zap"
)

// RackBatchProcessor 批量处理器
type RackBatchProcessor struct {
	store      store.Factory
	ctx        context.Context
	dryRun     bool
	batchSize  int
	skipErrors bool
}

// NewRackBatchProcessor 创建处理器
func NewRackBatchProcessor(dryRun bool, batchSize int, skipErrors bool) (*RackBatchProcessor, error) {
	// 初始化MongoDB连接
	mongoOpts, err := db.NewMongoOptions().Init()
	if err != nil {
		return nil, fmt.Errorf("初始化MongoDB失败: %v", err)
	}

	// 初始化MySQL连接
	mysqlHandler, err := db.NewMySQLOptions().Init()
	if err != nil {
		return nil, fmt.Errorf("初始化MySQL失败: %v", err)
	}

	// 初始化DataStore
	ds := &store.DataStore{
		Db:  mysqlHandler,
		Mgo: mongoOpts,
	}

	return &RackBatchProcessor{
		store:      ds,
		ctx:        context.Background(),
		dryRun:     dryRun,
		batchSize:  batchSize,
		skipErrors: skipErrors,
	}, nil
}

// ProcessCSVFile 处理CSV文件
func (p *RackBatchProcessor) ProcessCSVFile(filePath string) (*ProcessResult, error) {
	startTime := time.Now()

	color.Blue("开始处理CSV文件: %s", filePath)
	if p.dryRun {
		color.Yellow("运行在试运行模式，不会实际修改数据")
	}

	// 读取CSV文件
	records, err := p.readCSVFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取CSV文件失败: %v", err)
	}

	color.Green("成功读取 %d 条记录", len(records))

	// 初始化结果
	result := &ProcessResult{
		TotalRecords:   len(records),
		SuccessList:    make([]ProcessedRecord, 0),
		FailedList:     make([]FailedRecord, 0),
		SkippedList:    make([]SkippedRecord, 0),
		ConflictList:   make([]ConflictRecord, 0),
		ProcessingTime: "",
	}

	// 分批处理记录
	for i := 0; i < len(records); i += p.batchSize {
		end := min(i+p.batchSize, len(records))

		batch := records[i:end]
		color.Cyan("处理第 %d-%d 条记录...", i+1, end)

		if err := p.processBatch(batch, result); err != nil {
			if !p.skipErrors {
				return nil, fmt.Errorf("处理批次失败: %v", err)
			}
			color.Red("批次处理出错，跳过: %v", err)
		}
	}

	// 计算统计信息
	result.SuccessRecords = len(result.SuccessList)
	result.FailedRecords = len(result.FailedList)
	result.SkippedRecords = len(result.SkippedList)
	result.ProcessingTime = time.Since(startTime).String()

	return result, nil
}

// readCSVFile 读取CSV文件
func (p *RackBatchProcessor) readCSVFile(filePath string) ([]*RackPositionRecord, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)

	// 允许不同数量的字段 也就是说允许一些字段为空；
	reader.FieldsPerRecord = -1

	var records []*RackPositionRecord
	lineNumber := 0

	for {
		line, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("读取CSV行失败: %v", err)
		}

		lineNumber++

		// 跳过标题行
		if lineNumber == 1 && p.isHeaderLine(line) {
			continue
		}

		// 跳过空行
		if p.isEmptyLine(line) {
			continue
		}

		record, err := ParseCSVLine(line, lineNumber)
		if err != nil {
			if !p.skipErrors {
				return nil, fmt.Errorf("第%d行数据格式错误: %v", lineNumber, err)
			}
			color.Yellow("跳过第%d行，格式错误: %v", lineNumber, err)
			continue
		}

		records = append(records, record)
	}

	return records, nil
}

// isHeaderLine 判断是否为标题行
func (p *RackBatchProcessor) isHeaderLine(line []string) bool {
	if len(line) == 0 {
		return false
	}
	// 简单判断：如果第一列包含"序列号"等关键词，认为是标题行
	firstCol := strings.TrimSpace(line[0])
	return strings.Contains(firstCol, "序列号") ||
		strings.Contains(firstCol, "SN") ||
		strings.Contains(firstCol, "sn")
}

// isEmptyLine 判断是否为空行
func (p *RackBatchProcessor) isEmptyLine(line []string) bool {
	// 遍历这一行的每个字段
	for _, field := range line {
		// 如果这个字段不为空，则认为这一行不是空行
		if strings.TrimSpace(field) != "" {
			return false
		}
	}
	return true
}

// processBatch 处理一批记录
func (p *RackBatchProcessor) processBatch(records []*RackPositionRecord, result *ProcessResult) error {
	for _, record := range records {
		if err := p.processRecord(record, result); err != nil {
			if !p.skipErrors {
				return err
			}
			// 记录失败但继续处理
			result.FailedList = append(result.FailedList, FailedRecord{
				LineNumber:   record.LineNumber,
				SerialNumber: record.SerialNumber,
				RackName:     record.RackName,
				StartU:       record.StartU,
				Height:       record.Height,
				Error:        err.Error(),
				Suggestion:   "请检查数据格式和系统状态",
			})
		}
	}
	return nil
}

// processRecord 处理单条记录
func (p *RackBatchProcessor) processRecord(record *RackPositionRecord, result *ProcessResult) error {
	zap.L().Debug("处理记录",
		zap.String("sn", record.SerialNumber),
		zap.String("rack", record.RackName),
		zap.Int("start_u", record.StartU))

	// 1. 查找设备
	deviceInfo, err := p.findDevice(record.SerialNumber)
	if err != nil {
		result.FailedList = append(result.FailedList, FailedRecord{
			LineNumber:   record.LineNumber,
			SerialNumber: record.SerialNumber,
			RackName:     record.RackName,
			StartU:       record.StartU,
			Height:       record.Height,
			Error:        fmt.Sprintf("查找设备失败: %v", err),
			Suggestion:   "请确认设备序列号是否正确",
		})
		return nil
	}

	// 2. 查找机柜
	rackInfo, err := p.findRack(record.RackName)
	if err != nil {
		result.FailedList = append(result.FailedList, FailedRecord{
			LineNumber:   record.LineNumber,
			SerialNumber: record.SerialNumber,
			RackName:     record.RackName,
			StartU:       record.StartU,
			Height:       record.Height,
			Error:        fmt.Sprintf("查找机柜失败: %v", err),
			Suggestion:   "请确认机柜名称是否正确",
		})
		return nil
	}

	// 3. 检查U位冲突
	conflict, err := p.checkUPositionConflict(rackInfo.ID, record.StartU, record.Height, deviceInfo.ID)
	if err != nil {
		return fmt.Errorf("检查U位冲突失败: %v", err)
	}

	if conflict != nil {
		result.ConflictList = append(result.ConflictList, ConflictRecord{
			LineNumber:     record.LineNumber,
			SerialNumber:   record.SerialNumber,
			RackName:       record.RackName,
			RequestedRange: fmt.Sprintf("%d-%d", record.StartU, record.StartU+record.Height-1),
			ConflictDevice: conflict.ConflictDevice,
			ConflictRange:  conflict.ConflictRange,
			ConflictType:   conflict.ConflictType,
		})

		result.FailedList = append(result.FailedList, FailedRecord{
			LineNumber:   record.LineNumber,
			SerialNumber: record.SerialNumber,
			RackName:     record.RackName,
			StartU:       record.StartU,
			Height:       record.Height,
			Error:        fmt.Sprintf("U位冲突: %s占用了%s", conflict.ConflictDevice, conflict.ConflictRange),
			Suggestion:   "请选择其他U位或先移除冲突设备",
		})
		return nil
	}

	// 4. 检查机柜高度限制
	if record.StartU+record.Height-1 > rackInfo.Height {
		result.FailedList = append(result.FailedList, FailedRecord{
			LineNumber:   record.LineNumber,
			SerialNumber: record.SerialNumber,
			RackName:     record.RackName,
			StartU:       record.StartU,
			Height:       record.Height,
			Error: fmt.Sprintf("超出机柜高度限制，机柜高度:%dU，请求范围:%d-%d",
				rackInfo.Height, record.StartU, record.StartU+record.Height-1),
			Suggestion: "请调整起始U位或设备高度",
		})
		return nil
	}

	// 5. 执行更新操作
	action, err := p.updateDevicePosition(deviceInfo, rackInfo, record)
	if err != nil {
		result.FailedList = append(result.FailedList, FailedRecord{
			LineNumber:   record.LineNumber,
			SerialNumber: record.SerialNumber,
			RackName:     record.RackName,
			StartU:       record.StartU,
			Height:       record.Height,
			Error:        fmt.Sprintf("更新设备位置失败: %v", err),
			Suggestion:   "请检查系统状态或联系管理员",
		})
		return nil
	}

	// 记录成功
	actionDisplay := action
	if p.dryRun {
		actionDisplay = action + " (dry-run)"
	}

	result.SuccessList = append(result.SuccessList, ProcessedRecord{
		LineNumber:   record.LineNumber,
		SerialNumber: record.SerialNumber,
		DeviceName:   deviceInfo.Name,
		DeviceID:     deviceInfo.ID,
		RackName:     record.RackName,
		RackID:       rackInfo.ID,
		StartU:       record.StartU,
		EndU:         record.StartU + record.Height - 1,
		Height:       record.Height,
		Owner:        record.Owner,
		Action:       actionDisplay,
	})

	return nil
}
