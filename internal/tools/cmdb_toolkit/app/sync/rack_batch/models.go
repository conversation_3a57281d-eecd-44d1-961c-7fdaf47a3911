package rack_batch

import (
	"fmt"
	"strconv"
	"strings"
)

// RackPositionRecord CSV记录结构
type RackPositionRecord struct {
	SerialNumber string `csv:"序列号"`
	RackName     string `csv:"机柜名称"`
	StartU       int    `csv:"起始U位"`
	Height       int    `csv:"设备高度"`
	Owner        string `csv:"负责人"`

	// 内部字段
	LineNumber int `csv:"-"` // 行号，用于错误报告
}

// Validate 验证记录有效性
func (r *RackPositionRecord) Validate() error {
	if strings.TrimSpace(r.SerialNumber) == "" {
		return fmt.Errorf("序列号不能为空")
	}
	if strings.TrimSpace(r.RackName) == "" {
		return fmt.Errorf("机柜名称不能为空")
	}
	if r.StartU <= 0 {
		return fmt.Errorf("起始U位必须大于0，当前值：%d", r.StartU)
	}
	if r.Height <= 0 {
		return fmt.Errorf("设备高度必须大于0，当前值：%d", r.Height)
	}
	return nil
}

// String 格式化输出
func (r *RackPositionRecord) String() string {
	return fmt.Sprintf("SN:%s, 机柜:%s, U位:%d-%d, 负责人:%s",
		r.SerialNumber, r.RackName, r.StartU, r.StartU+r.Height-1, r.Owner)
}

// ProcessResult 处理结果
type ProcessResult struct {
	TotalRecords   int               `json:"total_records"`
	SuccessRecords int               `json:"success_records"`
	FailedRecords  int               `json:"failed_records"`
	SkippedRecords int               `json:"skipped_records"`
	SuccessList    []ProcessedRecord `json:"success_list"`
	FailedList     []FailedRecord    `json:"failed_list"`
	SkippedList    []SkippedRecord   `json:"skipped_list"`
	ConflictList   []ConflictRecord  `json:"conflict_list"`
	ProcessingTime string            `json:"processing_time"`
}

// ProcessedRecord 成功处理的记录
type ProcessedRecord struct {
	LineNumber   int    `json:"line_number"`
	SerialNumber string `json:"serial_number"`
	DeviceName   string `json:"device_name"`
	DeviceID     string `json:"device_id"`
	RackName     string `json:"rack_name"`
	RackID       string `json:"rack_id"`
	StartU       int    `json:"start_u"`
	EndU         int    `json:"end_u"`
	Height       int    `json:"height"`
	Owner        string `json:"owner"`
	Action       string `json:"action"` // "create", "update", "move"
}

// FailedRecord 失败的记录
type FailedRecord struct {
	LineNumber   int    `json:"line_number"`
	SerialNumber string `json:"serial_number"`
	RackName     string `json:"rack_name"`
	StartU       int    `json:"start_u"`
	Height       int    `json:"height"`
	Error        string `json:"error"`
	Suggestion   string `json:"suggestion"`
}

// SkippedRecord 跳过的记录
type SkippedRecord struct {
	LineNumber   int    `json:"line_number"`
	SerialNumber string `json:"serial_number"`
	Reason       string `json:"reason"`
}

// ConflictRecord U位冲突记录
type ConflictRecord struct {
	LineNumber     int    `json:"line_number"`
	SerialNumber   string `json:"serial_number"`
	RackName       string `json:"rack_name"`
	RequestedRange string `json:"requested_range"` // "1-4"
	ConflictDevice string `json:"conflict_device"` // 冲突设备SN
	ConflictRange  string `json:"conflict_range"`  // 冲突设备占用的U位范围
	ConflictType   string `json:"conflict_type"`   // "device", "placeholder"
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	ID            string `json:"id"`
	SerialNumber  string `json:"serial_number"`
	Name          string `json:"name"`
	ModelCode     string `json:"model_code"`
	ModelName     string `json:"model_name"`
	CurrentRack   string `json:"current_rack"`
	CurrentStartU int    `json:"current_start_u"`
	CurrentHeight int    `json:"current_height"`
	Owner         string `json:"owner"`
}

// RackInfo 机柜信息
type RackInfo struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Height   int    `json:"height"`
	IDCName  string `json:"idc_name"`
	Location string `json:"location"`
}

// ParseCSVLine 解析CSV行数据
func ParseCSVLine(line []string, lineNumber int) (*RackPositionRecord, error) {
	if len(line) < 5 {
		return nil, fmt.Errorf("CSV行数据不完整，期望5列，实际%d列", len(line))
	}

	startU, err := strconv.Atoi(strings.TrimSpace(line[2]))
	if err != nil {
		return nil, fmt.Errorf("起始U位格式错误：%s", line[2])
	}

	height, err := strconv.Atoi(strings.TrimSpace(line[3]))
	if err != nil {
		return nil, fmt.Errorf("设备高度格式错误：%s", line[3])
	}

	record := &RackPositionRecord{
		SerialNumber: strings.TrimSpace(line[0]),
		RackName:     strings.TrimSpace(line[1]),
		StartU:       startU,
		Height:       height,
		Owner:        strings.TrimSpace(line[4]),
		LineNumber:   lineNumber,
	}

	return record, record.Validate()
}

// Summary 生成处理结果摘要
func (r *ProcessResult) Summary() string {
	var summary strings.Builder

	summary.WriteString(fmt.Sprintf("批量导入完成 - 处理时间: %s\n", r.ProcessingTime))
	summary.WriteString(fmt.Sprintf("总记录数: %d\n", r.TotalRecords))
	summary.WriteString(fmt.Sprintf("成功: %d, 失败: %d, 跳过: %d\n",
		r.SuccessRecords, r.FailedRecords, r.SkippedRecords))

	if len(r.ConflictList) > 0 {
		summary.WriteString(fmt.Sprintf("U位冲突: %d\n", len(r.ConflictList)))
	}

	return summary.String()
}
