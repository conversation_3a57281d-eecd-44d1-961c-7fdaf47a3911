package rack_batch

import (
	"github.com/spf13/cobra"
)

func NewRackBatchImportCommand() *cobra.Command {
	var (
		csvFile    string
		dryRun     bool
		batchSize  int
		skipErrors bool
	)

	cmd := &cobra.Command{
		Use:   "rack-batch",
		Short: "批量导入设备机柜位置信息",
		Long: `从CSV文件批量导入设备的机柜位置信息
CSV文件格式：序列号,机柜名称,起始U位,设备高度,负责人
示例：
SERVER001,IDC1-A01,1,2,张三
SWITCH002,IDC1-A01,5,1,李四`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return ImportRackPositions(csvFile, dryRun, batchSize, skipErrors)
		},
	}

	cmd.Flags().StringVarP(&csvFile, "file", "f", "", "CSV文件路径")
	cmd.Flags().BoolVarP(&dryRun, "dry-run", "d", false, "试运行模式，不实际执行更新操作")
	cmd.Flags().IntVarP(&batchSize, "batch-size", "b", 100, "批处理大小")
	cmd.Flags().BoolVarP(&skipErrors, "skip-errors", "s", false, "跳过错误，继续处理其他记录")

	_ = cmd.MarkFlagRequired("file")

	return cmd
} 