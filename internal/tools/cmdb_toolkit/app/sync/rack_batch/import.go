package rack_batch

import (
	"fmt"
	"os"
	"strings"

	"github.com/fatih/color"
)

// ImportRackPositions 批量导入机柜位置的主入口函数
func ImportRackPositions(csvFile string, dryRun bool, batchSize int, skipErrors bool) error {
	// 检查文件是否存在
	if _, err := os.Stat(csvFile); os.IsNotExist(err) {
		return fmt.Errorf("CSV文件不存在: %s", csvFile)
	}

	// 创建处理器
	processor, err := NewRackBatchProcessor(dryRun, batchSize, skipErrors)
	if err != nil {
		return fmt.Errorf("创建处理器失败: %v", err)
	}

	// 处理CSV文件
	result, err := processor.ProcessCSVFile(csvFile)
	if err != nil {
		return fmt.Errorf("处理CSV文件失败: %v", err)
	}

	// 显示控制台摘要
	displaySummary(result, dryRun)

	// 如果有失败记录且不是跳过错误模式，返回错误
	if result.FailedRecords > 0 && !skipErrors {
		return fmt.Errorf("处理过程中有 %d 条记录失败", result.FailedRecords)
	}

	return nil
}

// displaySummary 显示控制台摘要
func displaySummary(result *ProcessResult, dryRun bool) {
	color.Blue("\n" + strings.Repeat("=", 60))
	if dryRun {
		color.Yellow("批量导入预览结果 (试运行模式)")
	} else {
		color.Green("批量导入完成")
	}
	color.Blue(strings.Repeat("=", 60))

	// 基础统计
	fmt.Printf("处理时间: %s\n", result.ProcessingTime)
	fmt.Printf("总记录数: %d\n", result.TotalRecords)

	// 成功记录
	if result.SuccessRecords > 0 {
		color.Green("✓ 成功: %d 条", result.SuccessRecords)
		if len(result.SuccessList) > 0 {
			fmt.Println("\n成功记录详情:")
			for i, record := range result.SuccessList {
				if i >= 5 { // 只显示前5条
					color.Cyan("  ... 还有 %d 条成功记录", len(result.SuccessList)-5)
					break
				}
				actionColor := color.GreenString
				if record.Action == "move" {
					actionColor = color.YellowString
				}
				fmt.Printf("  [%d] %s -> %s U%d-%d (%s)\n",
					record.LineNumber,
					record.SerialNumber,
					record.RackName,
					record.StartU,
					record.EndU,
					actionColor(record.Action))
			}
		}
	}

	// 失败记录
	if result.FailedRecords > 0 {
		color.Red("✗ 失败: %d 条", result.FailedRecords)
		if len(result.FailedList) > 0 {
			fmt.Println("\n失败记录详情:")
			for i, record := range result.FailedList {
				if i >= 5 { // 只显示前5条
					color.Red("  ... 还有 %d 条失败记录", len(result.FailedList)-5)
					break
				}
				fmt.Printf("  [%d] %s: %s\n",
					record.LineNumber,
					record.SerialNumber,
					color.RedString(record.Error))
				if record.Suggestion != "" {
					fmt.Printf("      建议: %s\n", color.YellowString(record.Suggestion))
				}
			}
		}
	}

	// 跳过记录
	if result.SkippedRecords > 0 {
		color.Yellow("⚠ 跳过: %d 条", result.SkippedRecords)
	}

	// U位冲突详情
	if len(result.ConflictList) > 0 {
		color.Red("\nU位冲突详情:")
		conflictMap := make(map[string][]ConflictRecord)
		for _, conflict := range result.ConflictList {
			conflictMap[conflict.RackName] = append(conflictMap[conflict.RackName], conflict)
		}

		for rackName, conflicts := range conflictMap {
			fmt.Printf("  机柜 %s:\n", color.CyanString(rackName))
			for _, conflict := range conflicts {
				fmt.Printf("    [%d] %s 请求 %s，与 %s 的 %s 冲突\n",
					conflict.LineNumber,
					conflict.SerialNumber,
					conflict.RequestedRange,
					color.RedString(conflict.ConflictDevice),
					conflict.ConflictRange)
			}
		}
	}

	// 操作统计
	if result.SuccessRecords > 0 {
		actionStats := make(map[string]int)
		for _, record := range result.SuccessList {
			actionStats[record.Action]++
		}

		fmt.Println("\n操作统计:")
		for action, count := range actionStats {
			switch action {
			case "create", "create (dry-run)":
				color.Green("  新上架: %d", count)
			case "move", "move (dry-run)":
				color.Yellow("  移动: %d", count)
			case "update", "update (dry-run)":
				color.Cyan("  位置调整: %d", count)
			}
		}
	}

	// 建议和提示
	fmt.Println()
	if dryRun {
		color.Yellow("这是试运行结果，没有实际修改数据")
		color.Cyan("如果结果符合预期，请去掉 --dry-run 参数执行实际导入")
	} else if result.FailedRecords > 0 {
		color.Yellow("建议:")
		color.Yellow("1. 检查失败记录的错误信息和建议")
		color.Yellow("2. 修正CSV文件中的问题数据")
		color.Yellow("3. 可以使用 --skip-errors 参数跳过错误继续处理")
	}

	if len(result.ConflictList) > 0 {
		color.Red("\n⚠ 注意: 发现U位冲突，请处理后重新导入")
	}

	color.Blue(strings.Repeat("=", 60) + "\n")
}

// generateSampleCSV 生成示例CSV文件
func GenerateSampleCSV(outputPath string) error {
	sampleData := `序列号,机柜名称,起始U位,设备高度,负责人
SERVER001,IDC1-A01,1,2,张三
SERVER002,IDC1-A01,5,1,李四
SWITCH001,IDC1-B02,10,1,王五
SERVER003,IDC2-A01,1,4,赵六
`

	if err := os.WriteFile(outputPath, []byte(sampleData), 0644); err != nil {
		return fmt.Errorf("创建示例文件失败: %v", err)
	}

	color.Green("示例CSV文件已创建: %s", outputPath)
	return nil
}
