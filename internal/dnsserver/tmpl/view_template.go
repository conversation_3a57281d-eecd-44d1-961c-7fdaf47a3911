package tmpl

/*
view 配置文件模板设计说明
# Office View的设计

模块view中对应的acl控制，会显式的声明不要哪些key，从配置角度，我其实可以不用配置哪些不接收，因为如果不配置允许哪些
那么没有配置的默认就是不允许的。但是显式的声明便于运维管理，可以帮助快速定位允许哪些不允许哪些。可读性增强
view "y" {
    match-clients {
        key office_y_key;
        !key office_efc_key;
        !key office_xy_key;
        10.1.0.0/16;
    };
}
*/

// NamedConfViewsTemplate 生成named.conf.views文件的模板
const NamedConfViewsTemplate = `# named.conf.views - DNS view configurations
# Generated at: {{.GeneratedAt}}
{{- if .ServerRole}}
# Server Role: {{.ServerRole}}
{{- end}}

{{- if .ServerRole}}
{{- if eq .ServerRole "master"}}
{{- range .Views}}
# View: {{.ViewName}}
view "{{.ViewName}}" {
    match-clients {
        {{- $currentViewName := .ViewName}}
        {{- range $.Views}}
            {{- if eq .ViewName $currentViewName}}
        key {{.KeyName}};
            {{- else}}
        !key {{.KeyName}};
            {{- end}}
        {{- end}}
        {{- if .ACLName}}
        {{.ACLName}};
        {{- end}}
    };
    allow-update { key {{.KeyName}}; };
    allow-transfer { key {{.KeyName}}; };
    {{- if $.SlaveServers}}
    also-notify {
        {{- $keyName := .KeyName}}
        {{- range $.SlaveServers}}
        {{.IP}} key {{$keyName}};
        {{- end}}
    };
    {{- end}}
    {{- if .AllowQuery}}
    allow-query {
        {{- range .AllowQuery}}
        {{.}};
        {{- end}}
    };
    {{- end}}
    {{- if .Recursion}}
    recursion {{.Recursion}};
    {{- end}}

    {{- if .ResponsePolicy}}
    response-policy {
        {{- range .ResponsePolicy.Zones}}
        zone "{{.}}";
        {{- end}}
    };
    {{- end}}

    {{- if .Forwarders}}
    forwarders {
        {{- range .Forwarders}}
        {{.}};
        {{- end}}
    };
    forward first;
    {{- end}}
    
    include "/etc/named.rfc1912.zones";
    include "/etc/named.root.key";
    include "/etc/named/zones/named.{{.ViewName}}.{{$.ServerRole}}.zones";
};
{{- end}}

# Default view
view "default" {
    match-clients {
        {{- range .Views}}
        !key {{.KeyName}};
        {{- end}}
        {{- if .DefaultView}}
        {{- if .DefaultView.ACLName}}
        {{.DefaultView.ACLName}};
        {{- else}}
        any;
        {{- end}}
        {{- else}}
        any;
        {{- end}}
    };
    allow-update { key default_key; };
    allow-transfer { key default_key; };
    {{- if .SlaveServers}}
    also-notify {
        {{- range .SlaveServers}}
        {{.IP}} key default_key;
        {{- end}}
    };
    {{- end}}

    {{- if .DefaultView}}
    {{- if .DefaultView.AllowQuery}}
    allow-query {
        {{- range .DefaultView.AllowQuery}}
        {{.}};
        {{- end}}
    };
    {{- end}}
    {{- if .DefaultView.Recursion}}
    recursion {{.DefaultView.Recursion}};
    {{- end}}

    {{- if .DefaultView.Forwarders}}
    forwarders {
        {{- range .DefaultView.Forwarders}}
        {{.}};
        {{- end}}
    };
    forward first;
    {{- end}}
    {{- end}}
    include "/etc/named.rfc1912.zones";
    include "/etc/named.root.key";
    include "/etc/named/zones/named.default.zones";
};

{{- else}}
# Slave server configuration
{{- range .Views}}
view "{{.ViewName}}" {
    match-clients {
        {{- $currentViewName := .ViewName}}
        {{- range $.Views}}
            {{- if eq .ViewName $currentViewName}}
        key {{.KeyName}};
            {{- else}}
        !key {{.KeyName}};
            {{- end}}
        {{- end}}
        {{- if $.MasterServers}}
        {{- range $.MasterServers}}
        !{{.IP}}/32;
        {{- end}}
        {{- end}}
        {{- if .ACLName}}
        {{.ACLName}};
        {{- end}}
    };
    allow-transfer { key {{.KeyName}}; };

    {{- if .ResponsePolicy}}
    response-policy {
        {{- range .ResponsePolicy.Zones}}
        zone "{{.}}";
        {{- end}}
    };
    {{- end}}
	
    include "/etc/named.rfc1912.zones";
    include "/etc/named.root.key";
    include "/etc/named/zones/named.{{.ViewName}}.{{$.ServerRole}}.zones";
};
{{- end}}

view "default" {
    match-clients {
        {{- range .Views}}
        !key {{.KeyName}};
        {{- end}}
        {{- if .MasterServers}}
        {{- range .MasterServers}}
        !{{.IP}}/32;
        {{- end}}
        {{- end}}
        {{- if .DefaultView}}
        {{- if .DefaultView.ACLName}}
        {{.DefaultView.ACLName}};
        {{- else}}
        any;
        {{- end}}
        {{- else}}
        any;
        {{- end}}
    };
    allow-transfer { key default_key; };

    include "/etc/named.rfc1912.zones";
    include "/etc/named.root.key";
    include "/etc/named/zones/named.default.zones";
};
{{- end}}

{{- end}}

# End of named.conf.views
`
