package openapi

import (
	"testing"

	"ks-knoc-server/config/knoc"
	"ks-knoc-server/pkg/mapdata"
)

const (
	processKey = "ITDataCenterProcess"
)

func InitKconf() error {
	return knoc.InitConfig()
}

func TestGenerateBPMBusinessID(t *testing.T) {
	businessId := GenerateBPMBusinessID(processKey)
	t.Log(businessId)
}

func TestProcessStartOnRack(t *testing.T) {
	if err := InitKconf(); err != nil {
		t.Error(err)
		return
	}

	title := "这是一个测试的流程"
	username := "maxiaoyu"
	vars := mapdata.MapData{
		"dc_type":   "onrack",
		"reinstall": "false",
	}
	businessId := GenerateBPMBusinessID(processPrefix)
	if _, err := ProcessStart(title, processKey, businessId, username, vars); err != nil {
		t.<PERSON><PERSON>r(err)
	} else {
		t.Log("success")
	}
}

func TestProcessStartCabling(t *testing.T) {
	if err := InitKconf(); err != nil {
		t.Error(err)
		return
	}

	title := "这是一个测试的流程"
	username := "maxiaoyu"
	vars := mapdata.MapData{
		"dc_type": "cabling",
	}
	businessId := GenerateBPMBusinessID(processPrefix)
	if _, err := ProcessStart(title, processKey, businessId, username, vars); err != nil {
		t.Error(err)
	} else {
		t.Log("success")
	}
}

func TestProcessPassOrReject(t *testing.T) {
	if err := InitKconf(); err != nil {
		t.Error(err)
		return
	}
	body := &ProcessPassOrRejectRequest{
		Comments:   "测试拒绝",
		ProcessKey: processKey,
		UserId:     "maxiaoyu",
	}
	_, err := ProcessReject(body)
	if err != nil {
		t.Error(err)
	}
	t.Log("Reject success")
}

func TestFrontAddSign(t *testing.T) {
	if err := InitKconf(); err != nil {
		t.Error(err)
	}
	r := &AddSignRequest{
		ProcessKey:   processKey,
		Comments:     "测试前加签",
		Assignee:     "wangzhichao03",
		TargetNodeId: "application",
		UserId:       "maxiaoyu",
		TaskId:       "1143189325",
	}
	if _, err := FrontAddSign(r); err != nil {
		t.Error(err)
	}
}

func TestGetProcessDetail(t *testing.T) {
	if err := InitKconf(); err != nil {
		t.Error(err)
	}
	result, err := ProcessDetail("it_dc_process_20241112184015")
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(result)
}
