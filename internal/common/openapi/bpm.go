package openapi

import (
	"errors"
	"fmt"
	"ks-knoc-server/internal/common/requests"
	"ks-knoc-server/pkg/mapdata"
	"time"

	"go.uber.org/zap"
)

const processPrefix = "it_dc_process"

// GenerateBPMBusinessID 返回用户BPM工单BusinessID
// 用户工单逻辑为BPM流程prefix + 时间戳，时间戳只精确到秒
// 示例：it_dc_process_20211213142306
func GenerateBPMBusinessID(prefix string) string {
	return fmt.Sprintf("%s_%s", prefix, time.Now().Format("20060102150405"))
}

// BaseOpenAPIResponse 基础的OpenAPI返回结构
// 返回code为0表示成功，其他情况均为失败
type BaseOpenAPIResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type ProcessStartRequest struct {
	BusinessExplain  string          `json:"businessExplain"`
	BusinessId       string          `json:"businessId" binding:"required"`
	ProcessKey       string          `json:"processKey" binding:"required"`
	Username         string          `json:"username" binding:"required"`
	ProcessVariables mapdata.MapData `json:"processVariables"`
}

// ProcessStart 流程启动
func ProcessStart(title, processKey, businessIdOrPrefix, username string, vars mapdata.MapData) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	// 直接使用传入的businessIdOrPrefix作为businessId
	// 如果是新工单，调用方应该传入已生成的businessId
	// 如果是重启工单，调用方会传入具体的businessId
	businessId := businessIdOrPrefix

	// 针对business_id做一下判断
	if businessId == "" {
		return nil, errors.New("business_id为空，请检查")
	}

	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/task/start",
		Empowerment: &resp,
		Body: &ProcessStartRequest{
			BusinessExplain:  title,
			BusinessId:       businessId,
			ProcessKey:       processKey,
			Username:         username,
			ProcessVariables: vars,
		},
	}
	if err := NewOpenapi().Post(req); err != nil {
		zap.L().Error("启动工单失败", zap.Error(err), zap.String("工单ID", businessId))
		return nil, err
	}

	if resp.Code != 0 {
		zap.L().Error("启动工单失败", zap.String("工单ID", businessId), zap.String("错误码", fmt.Sprintf("%d", resp.Code)), zap.String("错误信息", resp.Message))
		return &resp, fmt.Errorf("start process failed, code: %d, message: %s", resp.Code, resp.Message)
	}

	return &resp, nil
}

type TerminateForInitiatorRequest struct {
	BusinessId       string          `json:"businessId" binding:"required"`
	Username         string          `json:"username" binding:"required"`
	Comments         string          `json:"comments"`
	ProcessVariables mapdata.MapData `json:"processVariables"`
}

// TerminateForInitiator 发起人终止流程
func TerminateForInitiator(reqBody *TerminateForInitiatorRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/process/terminateForInitiator",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("terminate process failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

type SignalReceivedRequest struct {
	BusinessId string          `json:"businessId" binding:"required"`
	SignalName string          `json:"signalName" binding:"required"`
	Variables  mapdata.MapData `json:"variables"`
}

// SignalReceived 信号接收，用于接收任务并执行完成后，回调BPM以推进流程节点通过
func SignalReceived(reqBody *SignalReceivedRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/signalReceived",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("signal received failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

type AddSignRequest struct {
	ProcessKey   string `json:"processKey" binding:"required"`
	Comments     string `json:"comments"`
	Assignee     string `json:"assignee" binding:"required"`
	TargetNodeId string `json:"targetNodeId" binding:"required"`
	UserId       string `json:"userId" binding:"required"`
	TaskId       string `json:"taskId" binding:"required"`
}

// AfterAddSign 后加签，后加签指的是在你的审批节点通过以后，你想再让其他人进行一下审批
func AfterAddSign(reqBody *AddSignRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/task/afterAddSign",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("afterAddSign failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

// FrontAddSign 前加签指的是在通过你的审批之前，再加一道审批，此时前加签审批通过后会跳回到刚才你所在的审批节点再次审批
func FrontAddSign(reqBody *AddSignRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/task/frontAddSign",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("frontAddSign failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

type ConsultRequest struct {
	ProcessKey   string `json:"processKey" binding:"required"`
	Comments     string `json:"comments" binding:"required"`
	Assignee     string `json:"assignee" binding:"required"`
	TargetNodeId string `json:"targetNodeId" binding:"required"`
	UserId       string `json:"userId" binding:"required"`
	TaskId       string `json:"taskId" binding:"required"`
}

// Consult 征询
func Consult(reqBody *ConsultRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/task/consult",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("consult failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

type ShiftSignRequest struct {
	Assignee    string   `json:"assignee"`
	Comments    string   `json:"comments"`
	OldAssignee string   `json:"oldAssignee"`
	TaskIds     []string `json:"taskIds"`
	UserId      string   `json:"userId"`
}

func ShiftSign(reqBody *ShiftSignRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/task/shiftSign",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return &resp, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("shiftSign failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

type ProcessPassOrRejectRequest struct {
	Comments     string `json:"comments" binding:"required"`
	ProcessKey   string `json:"processKey" binding:"required"`
	TargetNodeId string `json:"targetNodeId" binding:"required"`
	TaskId       string `json:"taskId" binding:"required"`
	UserId       string `json:"userId" binding:"required"`
	Variables    any    `json:"variables"`
}

func ProcessPass(reqBody *ProcessPassOrRejectRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/task/pass",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return &resp, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("process pass failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

func ProcessReject(reqBody *ProcessPassOrRejectRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/task/reject",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return &resp, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("process reject failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

type HasReadAuthorityRequest struct {
	BusinessId string `json:"businessId" binding:"required"`
	Username   string `json:"username" binding:"required"`
}

func HasReadAuthority(reqBody *HasReadAuthorityRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/docAuth/hasReadAuthority",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return &resp, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("hasReadAuthority failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

type ProcessBackRequest struct {
	TaskDefKey string `json:"taskDefKey"`
	BackReason string `json:"backReason"`
	Username   string `json:"username"`
	BusinessId string `json:"businessId"`
}

// ProcessBack 申请人退回流程
func ProcessBack(reqBody *ProcessBackRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/back",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return &resp, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("process back failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

type ProcessDetailTasks struct {
	ID              string `json:"id"`                // id 无需关注
	TaskKey         string `json:"taskKey"`           // 任务key
	TaskID          string `json:"taskId"`            // 任务ID
	TaskName        string `json:"taskName"`          // 任务名称
	Extend1         string `json:"extend1,omitempty"` // 扩展字段1
	Extend2         string `json:"extend2,omitempty"` // 扩展字段2
	Extend3         string `json:"extend3,omitempty"` // 扩展字段3
	IsAgentApprover int    `json:"isAgentApprover"`
	UserIDAgented   string `json:"userIdAgented,omitempty"`
	UserUsername    string `json:"userUsername"`
	UserID          string `json:"userId"`                    // 审批人工号
	UserName        string `json:"userName"`                  // 审批人用户名
	Attachments     []any  `json:"attachments"`               // 附件
	OperationName   string `json:"operationName,omitempty"`   // 操作名称, 比如提交
	Operation       string `json:"operation,omitempty"`       // 操作标识, 比如SUBMIT
	OperationResult string `json:"operationResult,omitempty"` // 操作结果, 比如FrontAddSign
	CreateTime      string `json:"createTime"`                // 创建时间
	ApproveTime     string `json:"approveTime,omitempty"`     // 审批时间
	Comments        string `json:"comments,omitempty"`        // 审批评论
}

type ProcessDetailInfo struct {
	BusinessID               string               `json:"businessId"`               // 工单ID
	FormType                 string               `json:"formType"`                 // FormType 专业流程就是ProcessKey
	WorkFlowID               string               `json:"workFlowId"`               // 工作流ID
	WorkFlowName             string               `json:"workFlowName"`             // 工作流名称
	ApplyExplain             string               `json:"applyExplain"`             // 工单标题
	InstanceID               string               `json:"instanceId"`               // InstanceID，暂时还没用到
	InitiatorID              string               `json:"initiatorId"`              // 提交人工号
	InitiatorName            string               `json:"initiatorName"`            // 提交人姓名
	CurrentAuditName         string               `json:"currentAuditName"`         // 当前审批人姓名
	CurrentAuditUserNameList []string             `json:"currentAuditUserNameList"` // 当前审批人姓名列表
	InitiatorOrg             string               `json:"initiatorOrg"`             // 发起人部门（加密的，全是***）
	InitiatorTime            string               `json:"initiatorTime"`            // 发起时间
	Avatar                   string               `json:"avatar"`                   // 发起人头像
	ProcessState             string               `json:"processState"`             // 流程状态
	ProcessStateName         string               `json:"processStateName"`         // 流程状态中文名称
	DetailTasks              []ProcessDetailTasks `json:"detailTasks"`              // 审批记录列表
	ProcessKey               string               `json:"processKey"`               // 流程key
	HasToDoTask              bool                 `json:"hasToDoTask"`              // 是否还有待办任务
}

type ProcessDetailResponse struct {
	BaseOpenAPIResponse
	ProcessDetailInfo `json:"result"`
}

func NewProcessDetailResponse() *ProcessDetailResponse {
	return &ProcessDetailResponse{
		BaseOpenAPIResponse: BaseOpenAPIResponse{},
		ProcessDetailInfo:   ProcessDetailInfo{},
	}
}

// ProcessDetail 查询工单审批记录
func ProcessDetail(businessId string) (*ProcessDetailResponse, error) {
	resp := NewProcessDetailResponse()
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/detail",
		Empowerment: resp,
		Body:        map[string]string{"businessId": businessId},
	}
	if err := NewOpenapi().Post(req); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return resp, fmt.Errorf("process reject failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return resp, nil
}

type ProcessInformRequest struct {
	BusinessID         string   `json:"businessId" binding:"required"`
	Comments           string   `json:"comments" binding:"required"`
	NoticeUserNameList []string `json:"noticeUsernameList" binding:"required"`
	UserName           string   `json:"username" binding:"required"`
}

func ProcessInform(reqBody *ProcessInformRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/docAuth/relay",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return &resp, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("process inform failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

type ProcessCancelRequest struct {
	BusinessID string `json:"businessId" binding:"required"`
	Comments   string `json:"comments" binding:"required"`
	UserId     string `json:"userId" binding:"required"`
}

func ProcessCancel(reqBody *ProcessCancelRequest) (*BaseOpenAPIResponse, error) {
	var resp BaseOpenAPIResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v1/process/task/cancel",
		Empowerment: &resp,
		Body:        reqBody,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return &resp, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("process cancel failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

type BPMUserTokenRequest struct {
	UserToken string `json:"userToken"` // 用户token
}

type BPMUserTokenResponse struct {
	Code    string `json:"code"` // 响应码
	Message string `json:"msg"`  // 响应消息
	Data    struct {
		UserName string `json:"username"`  // 用户名
		ExpireIn string `json:"expire_in"` // 过期时间
	}
}

// GetByUserTokenRequest 用户token查询BPM工单请求
func GetByUserToken(req *BPMUserTokenRequest) (*BPMUserTokenResponse, error) {
	var resp BPMUserTokenResponse
	request := &requests.Request{
		URL:         "/bpm-openapi/api/v1/sso/getByUserToken",
		Empowerment: &resp,
		Body:        req,
	}
	if err := NewOpenapi().Post(request); err != nil {
		return nil, err
	}
	if resp.Code != "0" {
		return nil, fmt.Errorf("根据用户token查询用户信息失败, code: %s, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

// AgileFormContentResponse 快流程表单内容查询返回结果
type AgileFormContentResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	Timestamp string `json:"timestamp"`
	Result    struct {
		Persons struct {
			Num52244 struct {
				UserName         string `json:"user_name"`
				EmployeeName     string `json:"employee_name"`
				EmployeeEnName   string `json:"employee_en_name"`
				EmployeeJaName   string `json:"employee_ja_name"`
				EmployeeID       string `json:"employee_id"`
				Email            string `json:"email"`
				AvatarURL        string `json:"avatar_url"`
				DepartmentID     string `json:"department_id"`
				DepartmentName   string `json:"department_name"`
				DepartmentEnName string `json:"department_en_name"`
				LeaderEid        string `json:"leader_eid"`
				Terminated       bool   `json:"terminated"`
				TenantID         string `json:"tenant_id"`
				ExternalUser     bool   `json:"external_user"`
				ShowInformation  int    `json:"show_information"`
				IsShowChatButton bool   `json:"is_show_chat_button"`
				OrgName          string `json:"org_name"`
				OrgEnName        string `json:"org_en_name"`
				OrgJaName        string `json:"org_ja_name"`
				TopDept          string `json:"top_dept"`
				TopDeptName      string `json:"top_dept_name"`
			} `json:"52244"`
		} `json:"persons"`
		ModelData string `json:"model_data"`
	} `json:"result"`
	Fail          bool   `json:"fail"`
	Msg           string `json:"msg"`
	Success       bool   `json:"success"`
	LocaleMessage string `json:"localeMessage"`
}

func GetAgileFormContent(formCode, businessId string) (*AgileFormContentResponse, error) {
	var resp AgileFormContentResponse
	req := &requests.Request{
		URL:         "/bpm-openapi/api/v2/open/process/queryFormData4Open",
		Empowerment: &resp,
		Body:        map[string]string{"form_code": formCode, "biz_key": businessId},
	}
	if err := NewOpenapi().Post(req); err != nil {
		return nil, err
	}
	// 快流程的这个接口比较奇怪，返回的code是000000，然后msg在正常情况下是一个空字符串，然后success字段是一个bool，返回的是true。
	if resp.Code != "000000" {
		return &resp, fmt.Errorf("get agile form content failed, code: %s, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}
