package v1

import (
	"fmt"
	"strconv"
	"strings"

	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/utils"

	"go.uber.org/zap"
)

const (
	DefaultRackHeight int = 42
)

type Rack ModelData

// Height 机柜的高度
// 2024-06-04 根据实际的机柜高度进行计算
func (r Rack) Height() int {
	height, exist := r.Data["rack_height"]
	// 如果说不存在的话，走默认
	if !exist {
		return DefaultRackHeight
	}

	h, err := strconv.Atoi(utils.ToString(height))
	if err != nil {
		zap.L().Error(err.Error())
		return DefaultRackHeight
	}

	if h == 0 {
		zap.L().Error("机柜高度设置不合法", zap.String("height", height.(string)))
		return 0
	}

	return h
}

func (r Rack) Name() string {
	key := fmt.Sprintf("%s_name", r.ModelCode)
	if name, ok := r.Data[key]; ok {
		return utils.ToString(name)
	}
	return ""
}

func (r Rack) RackNumber() string {
	num, exist := r.Data["rack_number"]
	if !exist {
		return ""
	}
	return num.(string)
}

// VacancyRate 可以先写死了，后面再改
func (r Rack) VacancyRate() int {
	return 50
}

// Power 可以先写死了，后面再改
func (r Rack) Power() int {
	return 1000
}

// Position 机柜的位置信息
func (r Rack) Position() (int, int, error) {
	posString, exist := r.Data["rack_position"]
	if !exist {
		return 0, 0, errno.ErrRackPositionNotSet.Add("机柜位置信息不存在")
	}

	pos := posString.(string)
	posSlice := strings.Split(pos, ",")
	if len(posSlice) != 2 {
		zap.L().Error("机柜位置设置不合法", zap.String("pos", pos))
		return 0, 0, errno.ErrRackPositionInvalid.Add("机柜位置设置不合法")
	}

	x := utils.ToInt(posSlice[0])
	y := utils.ToInt(posSlice[1])

	return x, y, nil
}

func ToRackData(m ModelData) (Rack, error) {
	if m.ModelCode != "rack" {
		return Rack{}, errno.ErrModelDataMisMatch.Add("要转换的数据并不是rack类型")
	}
	return Rack(m), nil
}