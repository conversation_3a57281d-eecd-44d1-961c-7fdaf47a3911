package v1

type DT string

const (
	DeviceTypeStandard    DT = "standard"     // 标准设备，特指交换机，服务器，存储等可以单独占满一整个U位宽度的，这类设备一般是一个单元就放一个
	DeviceTypeNonStandard DT = "non_standard" // 非标准设备，比如打包机，macmini，台式机这一类的，一个U位宽度可以放多个。
	DeviceTypeDCInfra     DT = "dc_infra"     // DataCenterInfrastructure，机房基础设施，比如配线架，ODF，PDU
	DeviceTypeEmpty       DT = "empty"        // 空闲U位
	DeviceTypeUnknown     DT = "unknown"      // 未知分类
)

type Device interface {
	DataID() string
	StartU() int
	Height() int
	EndU() int
	Range() ([2]int, bool)
	SN() string
	DeviceType() DT
}
