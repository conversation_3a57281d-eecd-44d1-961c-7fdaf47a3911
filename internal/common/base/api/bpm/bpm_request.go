package bpm

import (
	"errors"
	"fmt"
	"strings"

	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"
)

// DCRequest 机房业务内流程请求
type DCRequest struct {
	Title              string          `json:"title" binding:"required"`                                 // 必填：工单标题
	OpType             DCOperationType `json:"op_type" binding:"required"`                               // 必填：操作类型
	Initiator          string          `json:"initiator"`                                                // 必填：发起人
	Owner              string          `json:"owner" mapstructure:"owner"`                               // 选填：负责人
	Office             string          `json:"office" binding:"required" mapstrcture:"office"`           // 必填：操作职场
	Note               string          `json:"note" mapstructure:"note"`                                 // 选填：备注
	DesireDeliveryTime string          `json:"desire_delivery_time" mapstructure:"desire_delivery_time"` // 选填: 期望交付时间
	DCRequest          []any           `json:"dc_request" binding:"required"`                            // 必填：具体工单内容
}

// Validate 校验工单请求
func (d *DCRequest) Validate() error {
	if d.Title == "" {
		return errors.New("工单标题不能为空")
	}

	if d.OpType == "" {
		return errors.New("操作类型不能为空")
	}

	if d.Initiator == "" {
		return errors.New("发起人不能为空")
	}

	if d.Office == "" {
		return errors.New("操作职场不能为空")
	}

	if len(d.DCRequest) == 0 {
		return errors.New("工单内容不能为空, 至少需要存在一条要操作的数据")
	}

	return nil
}

// DCOnRackDeviceRequest 上线设备
type DCOnRackDeviceRequest struct {
	DeviceType      string       `json:"device_type" binding:"required" mapstructure:"device_type"`   // 必填: 设备类型
	DeviceSN        string       `json:"device_sn" binding:"required" mapstructure:"device_sn"`       // 必填: 设备SN
	DeviceBrand     [2]string    `json:"device_brand" binding:"required" mapstructure:"device_brand"` // 必填: 设备品牌和型号，因为是级联数据，所以接收的是一个长度为2的数组，1号位是品牌，2号位是型号
	ServiceInfo     string       `json:"service_info" binding:"required" mapstructure:"service_info"` // 必填: 服务说明信息
	OverdueTime     string       `json:"overdue_time" mapstructure:"overdue_time"` // 必填: 过保时间
	Owner           string       `json:"owner" mapstructure:"owner"`                                  // 负责人，由DcRequest中的Owner来确认
	IdcRoom         string       `json:"idc_room" mapstructure:"idc_room"`                            // 选填: 设备上架机房
	DeviceRack      string       `json:"device_rack" mapstructure:"device_rack"`                      // 选填: 设备上架机柜
	DevicePosition  int          `json:"device_position" mapstructure:"device_position"`              // 选填: 设备上架位置
	Power           int          `json:"power" mapstructure:"power"`                                  // 选填: 电量
	DeviceHeight    int          `json:"device_height" mapstructure:"device_height"`                  // 选填: 设备高度，所占U位
	HardWareMonitor bool         `json:"hardware_monitor" mapstructure:"hardware_monitor"`            // 选填: 是否需要硬件监控, 如果是服务器, 默认选是
	InterfaceBond   bool         `json:"interface_bond"  mapstructure:"interface_bond"`               // 选填: 是否需要网卡绑定，如果是服务器, 默认绑定
	ReInstall       bool         `json:"reinstall" mapstructure:"reinstall"`                          // 选填: 是否需要重新安装，如果是服务器, 默认不重装
	SystemFamily    SystemFamily `json:"system_family" mapstructure:"system_family"`                  // 选填: 操作系统
	RaidGroup       RaidGroup    `json:"raid_group" mapstructure:"raid_group"`                        // 选填: raid卡组
	IPAddress       string       `json:"ip_address" mapstructure:"ip_address"`                        // 选填: 内网IP地址
	IPNetMask       string       `json:"ip_netmask" mapstructure:"ip_netmask"`                        // 选填: 内网IP掩码
	IPGateway       string       `json:"ip_gateway" mapstructure:"ip_gateway"`                        // 选填: 内网网关
	IPDNS           string       `json:"ip_dns" mapstructure:"ip_dns"`                                // 选填: 内网DNS
	BMCIPAddress    string       `json:"bmc_ip_address" mapstructure:"bmc_ip_address"`                // 选填: BMC IP地址
	BMCNetMask      string       `json:"bmc_netmask" mapstructure:"bmc_netmask"`                      // 选填: BMC 子网掩码
	BMCGateway      string       `json:"bmc_gateway" mapstructure:"bmc_gateway"`                      // 选填: BMC 网关
}

// Validate 校验设备信息
func (d *DCOnRackDeviceRequest) Validate() error {
	if d.DeviceType == "" {
		return errors.New("设备类型不能为空")
	}

	if d.DeviceSN == "" {
		return errors.New("设备序列号不能为空")
	}

	if d.DeviceBrand[0] == "" {
		return errors.New("设备品牌不能为空")
	}

	if d.ServiceInfo == "" {
		return errors.New("服务说明不能为空")
	}

	return nil
}

func (d *DCOnRackDeviceRequest) Format() error {
	// 目前主要修复一下时间，提交过来的可能
	if d.OverdueTime != "" {
		t, err := utils.ParseISO8601DateString(d.OverdueTime)
		if err != nil {
			fmt.Println("解析时间错误:", err)
			return err
		}

		// 格式化为年月日
		dateStr := t.Format("2006-01-02")
		d.OverdueTime = dateStr
	}

	if d.DeviceSN != "" {
		d.DeviceSN = strings.ToUpper(d.DeviceSN)
	}

	return nil
}

// DCOnRackDevice 上线设备，用于保存到数据库中的结构
type DCOnRackDevice struct {
	DeviceType      string       `json:"device_type" binding:"required" mapstructure:"device_type"`   // 必填: 设备类型
	DeviceSN        string       `json:"device_sn" binding:"required" mapstructure:"device_sn"`       // 必填: 设备SN
	DeviceBrand     string       `json:"device_brand" binding:"required" mapstructure:"device_brand"` // 必填: 设备品牌和型号
	DeviceModel     string       `json:"device_model" binding:"required" mapstructure:"device_model"` // 必填: 设备型号
	ServiceInfo     string       `json:"service_info" binding:"required" mapstructure:"service_info"` // 必填: 服务说明信息
	OverdueTime     string       `json:"overdue_time" binding:"required" mapstructure:"overdue_time"` // 必填: 过保时间
	Owner           string       `json:"owner" mapstructure:"owner"`                                  // 负责人，由DcRequest中的Owner来确认
	IdcRoom         string       `json:"idc_room" mapstructure:"idc_room"`                            // 选填: 设备上架机房
	DeviceRack      string       `json:"device_rack" mapstructure:"device_rack"`                      // 选填: 设备上架机柜
	DevicePosition  int          `json:"device_position" mapstructure:"device_position"`              // 选填: 设备上架位置
	Power           int          `json:"power" mapstructure:"power"`                                  // 选填: 电量
	DeviceHeight    int          `json:"device_height" mapstructure:"device_height"`                  // 选填: 设备高度，所占U位
	HardWareMonitor bool         `json:"hardware_monitor,omitempty" mapstructure:"hardware_monitor"`  // 选填: 是否需要硬件监控, 如果是服务器, 默认选是
	InterfaceBond   bool         `json:"interface_bond,omitempty"  mapstructure:"interface_bond"`     // 选填: 是否需要网卡绑定，如果是服务器, 默认绑定
	ReInstall       bool         `json:"reinstall,omitempty" mapstructure:"reinstall"`                // 选填: 是否需要重新安装，如果是服务器, 默认不重装
	SystemFamily    SystemFamily `json:"system_family,omitempty" mapstructure:"system_family"`        // 选填: 操作系统
	RaidGroup       RaidGroup    `json:"raid_group,omitempty" mapstructure:"raid_group"`              // 选填: raid卡组
	IPAddress       string       `json:"ip_address,omitempty" mapstructure:"ip_address"`              // 选填: 内网IP地址
	IPNetMask       string       `json:"ip_netmask,omitempty" mapstructure:"ip_netmask"`              // 选填: 内网IP掩码
	IPGateway       string       `json:"ip_gateway,omitempty" mapstructure:"ip_gateway"`              // 选填: 内网网关
	IPDNS           string       `json:"ip_dns,omitempty" mapstructure:"ip_dns"`                      // 选填: 内网DNS
	BMCIPAddress    string       `json:"bmc_ip_address,omitempty" mapstructure:"bmc_ip_address"`      // 选填: BMC IP地址
	BMCNetMask      string       `json:"bmc_netmask,omitempty" mapstructure:"bmc_netmask"`            // 选填: BMC 子网掩码
	BMCGateway      string       `json:"bmc_gateway,omitempty" mapstructure:"bmc_gateway"`            // 选填: BMC 网关
}

var DcOnRackDeviceFieldName = map[string]string{
	"device_type":      "设备类型",
	"device_sn":        "设备序列号",
	"device_brand":     "设备品牌",
	"device_model":     "设备型号",
	"service_info":     "服务说明",
	"overdue_time":     "过保时间",
	"owner":            "负责人",
	"idc_room":         "机房",
	"device_rack":      "机柜",
	"device_position":  "位置",
	"power":            "电量",
	"device_height":    "设备高度",
	"hardware_monitor": "硬件监控",
	"interface_bond":   "网卡绑定",
	"reinstall":        "是否需要重新安装",
	"system_family":    "操作系统",
	"raid_group":       "raid卡组",
	"ip_address":       "内网IP地址",
	"ip_netmask":       "内网IP掩码",
	"ip_gateway":       "内网网关",
	"ip_dns":           "内网DNS",
	"bmc_ip_address":   "BMC IP地址",
	"bmc_netmask":      "BMC 子网掩码",
	"bmc_gateway":      "BMC 网关",
}

func NewDCOnRackRequest() []*DCOnRackDeviceRequest {
	return make([]*DCOnRackDeviceRequest, 0)
}

type DCOffRackDevice struct {
	Name       string `json:"name" binding:"required" mapstructure:"name"`               // 必填: 设备名称
	DeviceSN   string `json:"device_sn" binding:"required" mapstructure:"device_sn"`     // 必填: 设备SN
	DeviceType string `json:"device_type" binding:"required" mapstructure:"device_type"` // 必填: 设备类型
	Brand      string `json:"brand" mapstructure:"brand"`                                // 必填: 设备品牌
	Model      string `json:"model" mapstructure:"model"`                                // 选填: 设备型号, 原本数据可能未配置
	Office     string `json:"office" mapstructure:"office"`                              // 选填: 设备职场, 原本数据可能未配置
	IDC        string `json:"idc" mapstructure:"idc"`                                    // 选填: 设备IDC, 原本数据可能未配置
	Rack       string `json:"rack" mapstructure:"rack"`                                  // 选填: 设备机柜, 原本数据可能未配置
	Position   int    `json:"position" mapstructure:"position"`                          // 选填: 设备位置
	Owner      string `json:"owner" mapstructure:"owner"`                                // 选填: 设备负责人
}

var DcOffRackDeviceFieldName = map[string]string{
	"name":        "设备名称",
	"device_sn":   "设备序列号",
	"device_type": "设备类型",
	"brand":       "设备品牌",
	"model":       "设备型号",
	"office":      "设备职场",
	"idc":         "设备IDC",
	"rack":        "设备机柜",
	"position":    "设备位置",
	"owner":       "设备负责人",
}

func NewDCOffRackRequest() []*DCOffRackDevice {
	return make([]*DCOffRackDevice, 0)
}

// DCOperationDevice 操作设备
type DCOperationDevice struct {
	DeviceSN string `json:"device_sn" binding:"required" mapstructure:"device_sn"` // 设备SN
	OpInfo   string `json:"op_info" binding:"required" mapstructure:"op_info"`     // 操作信息
	Confirm  bool   `json:"confirm" mapstructure:"confirm"`                        // 操作前确认
}

var DcOperationDeviceFieldName = map[string]string{
	"device_sn": "设备序列号",
	"op_info":   "操作信息",
	"confirm":   "操作前确认",
}

// NewDCOperationRequest 操作设备
func NewDCOperationRequest() []*DCOperationDevice {
	return make([]*DCOperationDevice, 0)
}

// DCCablingRequest 综合布线
type DCCablingRequest struct {
	IdcRoom  string `json:"idc_room" binding:"required" mapstructure:"idc_room"` // 设备上架机房
	DocsLink string `json:"docs_link" mapstructure:"docs_link"`                  // 操作信息, 选填填,是一个Docs的链接
}

var DcCablingDeviceFieldName = map[string]string{
	"idc_room":  "机房",
	"docs_link": "操作信息",
}

// NewDCCablingRequest 综合布线
func NewDCCablingRequest() []*DCCablingRequest {
	return make([]*DCCablingRequest, 0)
}

type ProcessPassOrRejectRequest struct {
	BusinessID string `json:"business_id" binding:"required"`
	Comments   string `json:"comments"`
}

// ProcessPassRequest 流程通过
type ProcessPassRequest struct {
	ProcessPassOrRejectRequest
	Data mapdata.MapData `json:"data"`
}

type ProcessAddOrShiftSignRequest struct {
	BusinessID string `json:"business_id" binding:"required"`
	Comments   string `json:"comments" binding:"required"`
	UserName   string `json:"username" binding:"required"`
}

type ProcessInformRequest struct {
	BusinessID     string   `json:"business_id" binding:"required"`
	Comments       string   `json:"comments" binding:"required"`
	NoticeUserList []string `json:"notice_user_list" binding:"required"`
}

type ProcessStartRequest struct {
	Title      string `json:"title"`
	BusinessID string `json:"business_id" binding:"required"`
}

type ProcessCancelRequest struct {
	BusinessID string `json:"business_id" binding:"required"`
	Comments   string `json:"comments" binding:"required"`
}
