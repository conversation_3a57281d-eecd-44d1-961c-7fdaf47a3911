package rest

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"reflect"
	"strings"
	"syscall"
	"time"

	"go.uber.org/zap"
)

type (
	// VerbType HTTP请求的类型
	VerbType string
	// Scheme HTTP请求的协议
	Scheme string
)

const (
	// PUT 表示http put方法
	PUT VerbType = http.MethodPut
	// POST 表示http post方法
	POST VerbType = http.MethodPost
	// GET 表示http get方法
	GET VerbType = http.MethodGet
	// DELETE 表示http delete方法
	DELETE VerbType = http.MethodDelete
	// PATCH 表示http patch方法
	PATCH VerbType = http.MethodPatch

	// SchemeHTTP  表示http协议
	SchemeHTTP Scheme = "http"
	// SchemeHTTPS 表示https协议
	SchemeHTTPS Scheme = "https"
)

func (s Scheme) String() string {
	return string(s) + "://"
}

// Request 请求
type Request struct {
	parent  *Client
	verb    VerbType
	params  url.Values
	headers http.Header
	body    []byte
	ctx     context.Context

	Client     HttpClient
	Credential *Credential

	// prefixed url
	baseURL string
	// sub path of the url, will be append to baseURL
	subPath string
	// sub path format args
	subPathArgs []any

	Scheme   Scheme
	Endpoint string
	Timeout  time.Duration

	peek bool
	err  error
}

// WithParams 附加请求参数
func (r *Request) WithParams(params map[string]string) *Request {
	if r.params == nil {
		r.params = make(url.Values)
	}
	for paramName, value := range params {
		r.params[paramName] = append(r.params[paramName], value)
	}
	return r
}

func (r *Request) WithScheme(scheme Scheme) *Request {
	r.Scheme = scheme
	return r
}

// WithParamsFromURL 从URL中解析参数
func (r *Request) WithParamsFromURL(u *url.URL) *Request {
	if r.params == nil {
		r.params = make(url.Values)
	}
	params := u.Query()
	for paramName, value := range params {
		r.params[paramName] = append(r.params[paramName], value...)
	}
	return r
}

func (r *Request) WithEndpoint(endpoint string) *Request {
	r.Endpoint = endpoint
	return r
}

// WithParam 附加请求参数，与WithParams的区别是，WithParam只能添加一个值
func (r *Request) WithParam(paramName, value string) *Request {
	if r.params == nil {
		r.params = make(url.Values)
	}
	r.params[paramName] = append(r.params[paramName], value)
	return r
}

// WithHeaders 添加请求头
// header: http.Header，是一个map[string][]string的别名
func (r *Request) WithHeaders(header http.Header) *Request {
	if r.headers == nil {
		r.headers = header
		return r
	}

	for key, values := range header {
		for _, v := range values {
			r.headers.Add(key, v)
		}
	}
	return r
}

// Peek TODO
func (r *Request) Peek() *Request {
	r.peek = true
	return r
}

// WithContext 设置上下文
func (r *Request) WithContext(ctx context.Context) *Request {
	r.ctx = ctx
	return r
}

// WithTimeout 设置超时时间
func (r *Request) WithTimeout(d time.Duration) *Request {
	r.Timeout = d
	return r
}

// subResource 子资源路径
func (r *Request) subResource(subPath string) *Request {
	subPath = strings.TrimLeft(subPath, "/")
	r.subPath = subPath
	return r
}

// SubResourcef TODO
func (r *Request) SubResourcef(subPath string, args ...any) *Request {
	r.subPathArgs = args
	return r.subResource(subPath)
}

func (r *Request) WithCredentials(cred *Credential) *Request {
	r.Credential = cred
	return r
}

// Body 设置RequestBody
func (r *Request) Body(body any) *Request {
	// 如果body为空的话，那么就初始化一个空的byte切片
	if body == nil {
		r.body = make([]byte, 0)
		return r
	}

	valueOf := reflect.ValueOf(body)
	switch valueOf.Kind() {
	case reflect.Interface:
		fallthrough
	case reflect.Map:
		fallthrough
	case reflect.Ptr:
		fallthrough
	case reflect.Slice:
		if valueOf.IsNil() {
			r.body = []byte("")
			return r
		}
		
	case reflect.Struct:

	default:
		r.err = errors.New("invalid body type, should be one of [interface, map, pointer, slice, struct]")
	}

	data, err := json.Marshal(body)
	if err != nil {
		r.err = err
		r.body = []byte("")
		return r
	}

	r.body = data
	return r
}

// WrapURL 封装URL到Request中
func (r *Request) WrapURL() *url.URL {
	// 初始化一个空的url.URL用来保存最终的url
	finalUrl := &url.URL{}
	if len(r.baseURL) != 0 {
		u, err := url.Parse(r.baseURL)
		if err != nil {
			r.err = err
			return new(url.URL)
		}
		*finalUrl = *u
	}

	if len(r.subPathArgs) > 0 {
		finalUrl.Path = finalUrl.Path + fmt.Sprintf(r.subPath, r.subPathArgs...)
	} else {
		finalUrl.Path = finalUrl.Path + r.subPath
	}

	query := url.Values{}
	for key, values := range r.params {
		for _, value := range values {
			query.Add(key, value)
		}
	}

	if r.Timeout != 0 {
		query.Set("timeout", r.Timeout.String())
	}

	finalUrl.RawQuery = query.Encode()
	return finalUrl
}

// Do 发送Request请求
func (r *Request) Do() *Response {
	// 初始化一个新的Response
	response := new(Response)

	// 如果Request中的err不为空，那么就将err赋值给Response中的err
	if r.err != nil {
		response.Err = r.err
		return response
	}

	client := r.Client
	if client == nil {
		client = http.DefaultClient
	}

	if r.Endpoint == "" {
		zap.L().Error("endpoint is empty", zap.String("endpoint", r.Endpoint), zap.Any("request", r))
		response.Err = fmt.Errorf("endpoint is empty")
		return response
	}

	if r.Scheme == "" {
		r.Scheme = SchemeHTTPS
	}

	maxRetryCycle := 3
	for try := 0; try < maxRetryCycle; try++ {
		targetUrl := r.Scheme.String() + r.Endpoint + r.WrapURL().String()
		req, err := http.NewRequest(string(r.verb), targetUrl, bytes.NewReader(r.body))
		if err != nil {
			response.Err = err
			return response
		}

		if r.ctx != nil {
			req = req.WithContext(r.ctx)
		}

		req.Header = CloneHeader(r.headers)
		if len(req.Header) == 0 {
			req.Header = make(http.Header)
		}
		// 删除 Accept-Encoding 避免返回值被压缩
		req.Header.Del("Accept-Encoding")
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Accept", "application/json")

		resp, err := client.Do(req)
		if err != nil {
			// "Connection reset by peer"是一个特殊的错误，大多数情况下它是一个暂时的错误。
			// 这也就意味着我们可以进行重试. 同样get操作也是如此，但是其他的"write"操作不能简单的重试，一般来说它们不是幂等的。
			zap.L().Error(fmt.Sprintf("%s %s with body %s, but %v", string(r.verb), targetUrl, r.body, err))
			if !isConnectionReset(err) || r.verb != GET {
				response.Err = err
				return response
			}

			// retry now
			time.Sleep(20 * time.Millisecond)
			continue

		}

		var body []byte
		if resp.Body != nil {
			data, err := io.ReadAll(resp.Body)
			if err != nil {
				if err == io.ErrUnexpectedEOF {
					// retry now
					time.Sleep(20 * time.Millisecond)
					continue
				}
				response.Err = err
				zap.L().Error(fmt.Sprintf("%s %s with body %s, err: %v", string(r.verb), targetUrl, r.body,
					err))
				return response
			}
			body = data
		}

		response.Body = body
		response.StatusCode = resp.StatusCode
		response.Status = resp.Status
		response.Header = resp.Header

		return response
	}

	response.Err = errors.New("unexpected error")
	return response
}

// isConnectionReset 判定是否是"connection reset by peer"错误
// Returns if the given err is "connection reset by peer" error.
func isConnectionReset(err error) bool {
	if urlErr, ok := err.(*url.Error); ok {
		err = urlErr.Err
	}
	if opErr, ok := err.(*net.OpError); ok {
		err = opErr.Err
	}
	if osErr, ok := err.(*os.SyscallError); ok {
		err = osErr.Err
	}
	if errno, ok := err.(syscall.Errno); ok && errno == syscall.ECONNRESET {
		return true
	}
	return false
}
