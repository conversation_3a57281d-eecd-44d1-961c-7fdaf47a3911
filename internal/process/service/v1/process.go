package v1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"time"

	bpmApi "ks-knoc-server/internal/common/base/api/bpm"
	message "ks-knoc-server/internal/common/base/message/process"
	"ks-knoc-server/internal/common/base/model/bpm"
	"ks-knoc-server/internal/common/openapi"
	"ks-knoc-server/internal/process/store"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"github.com/gin-gonic/gin/binding"
	"github.com/mitchellh/mapstructure"
	"go.elastic.co/apm"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	processKey    = "ITDataCenterProcess"
	processName   = "IT机房内业务流程"
	processPrefix = "it_dc_process"
)

const (
	BPMNodeStart       = "startEvent"
	BPMNodeApplication = "application"
	BPMNodeEnd         = "endEvent"
)

type ProcessService interface {
	// CreateDCProcess 创建上架流程
	CreateDCProcess(ctx context.Context, initiator string, req bpmApi.DCRequest) error
	// GetProcessList 获取工单列表
	GetProcessList(ctx context.Context, orderType, keyword, operator string, pageNumber, pageSizeNumber int64) (*bpmApi.OrderListResponse, error)
	// GetProcessDetail 获取工单详情
	GetProcessDetail(ctx context.Context, businessId string) (any, error)
	// RejectDCProcess 驳回工单
	RejectDCProcess(ctx context.Context, initiator string, req *bpmApi.ProcessPassOrRejectRequest) (any, error)
	// PassDCProcess 同意工单
	PassDCProcess(ctx context.Context, initiator string, req *bpmApi.ProcessPassRequest) error
	// GetOrderAuditRecord 获取工单审批记录
	GetOrderAuditRecord(ctx context.Context, businessId string) (*bpmApi.OrderAuditRecordResponse, error)
	// ProcessBack 申请人退回流程
	ProcessBack(ctx context.Context, initiator string, req *bpmApi.ProcessPassOrRejectRequest) error
	// FrontAddSign 前加签
	FrontAddSign(ctx context.Context, initiator string, req *bpmApi.ProcessAddOrShiftSignRequest) error
	// AfterAddSign 后加签
	AfterAddSign(ctx context.Context, initiator string, req *bpmApi.ProcessAddOrShiftSignRequest) error
	// ShiftSign 转签
	ShiftSign(ctx context.Context, initiator string, req *bpmApi.ProcessAddOrShiftSignRequest) error
	// Consult 征询
	Consult(ctx context.Context, initiator string, req *bpmApi.ProcessAddOrShiftSignRequest) error
	// Inform 知会
	Inform(ctx context.Context, initiator string, req *bpmApi.ProcessInformRequest) error
	// StartDCProcess 启动工单
	StartDCProcess(ctx context.Context, initiator string, req *bpmApi.ProcessStartRequest) error
	// CancelDCProcess 撤销工单
	CancelDCProcess(ctx context.Context, initiator string, req *bpmApi.ProcessPassOrRejectRequest) error
	// GetOrderInfo 获取工单详情
	GetOrderInfo(ctx context.Context, businessId string) (any, error)
	// GetOrderBase 获取工单基本信息
	GetOrderBase(ctx context.Context, businessId string) (*message.OrderBaseMessage, error)
	// GetOrderAuditLog 获取工单审批记录
	GetOrderAuditLog(ctx context.Context, businessId string) (message.OrderAuditLogMessages, error)
}

type processService struct {
	store store.Factory
}

func newProcessService(s store.Factory) ProcessService {
	return &processService{store: s}
}

// generateOrderTitle 生成工单标题
func (p *processService) generateOrderTitle(ctx context.Context, userTitle, processKey, opType string) string {
	zap.L().Debug("generateOrderTitle Service Called")
	span, _ := apm.StartSpan(ctx, "generateOrderTitle", "service")
	defer span.End()

	process, err := p.store.BPMProcess().GetProcessByKey(ctx, processKey)
	if err != nil {
		zap.L().Error("获取流程信息失败", zap.Error(err))
		return ""
	}

	// 获取当前流程分类的中文名称
	opTypeName := bpmApi.OpTypeNameMap[bpmApi.DCOperationType(opType)]

	// 返回最终的工单标题
	return fmt.Sprintf("%s-%s-%s", process.ProcessName, opTypeName, userTitle)
}

// RequestCheck 请求参数校验
func RequestCheck(body, req any) error {
	if err := mapstructure.Decode(body, req); err != nil {
		return err
	}

	if err := binding.Validator.ValidateStruct(req); err != nil {
		return err
	}

	return nil
}

func (p *processService) GetOrderAuditLog(ctx context.Context, businessId string) (message.OrderAuditLogMessages, error) {
	zap.L().Debug("GetOrderAuditLog Service Called")
	span, _ := apm.StartSpan(ctx, "GetOrderAuditLog", "service")
	defer span.End()

	logs, err := p.store.BPMExecutionLog().GetExecutionLogByBusinessID(businessId)
	if err != nil {
		zap.L().Error("GetOrderAuditLog Error", zap.Error(err))
		return nil, err
	}

	resp := make(message.OrderAuditLogMessages, 0)
	for _, log := range logs {
		status := bpm.ExecutionStatus(log.Status)

		approvers := make([]string, 0)
		approversList := strings.Split(log.ApprovalList, ",")
		for _, approver := range approversList {
			if strings.TrimSpace(approver) != "" {
				approvers = append(approvers, approver)
			}
		}

		resp = append(resp, message.OrderAuditLogMessage{
			ID:         log.ID,
			BusinessID: log.BusinessID,
			NodeName:   log.NodeName,
			Executor:   log.Executor,
			StatusCode: status.String(),
			Status:     status.GetI18nText(),
			Comments:   log.Comments,
			CreatedAt:  log.CreatedAt,
			UpdatedAt:  log.UpdatedAt,
			Approvers:  approvers,
		})
	}

	// 按照createAt从小到大排序，如果时间一致则按照id从小到大排序
	sort.Slice(resp, func(i, j int) bool {
		if resp[i].CreatedAt == resp[j].CreatedAt {
			return resp[i].ID < resp[j].ID
		}
		return resp[i].CreatedAt < resp[j].CreatedAt
	})

	return resp, nil
}

// CreateDCProcess 创建流程
// 这个流程要足够通用，因为可能创建上架的，下架的，操作的，布线的，所以我这里content传递的其实是any
func (p *processService) CreateDCProcess(ctx context.Context, initiator string, req bpmApi.DCRequest) error {
	zap.L().Debug("CreateDCProcess Service Called")
	span, _ := apm.StartSpan(ctx, "CreateDCProcess", "service")
	defer span.End()

	// 拆分office名称和office的ID
	officeSections := strings.Split(req.Office, "_")
	if len(officeSections) != 2 {
		zap.L().Error("提交的职场信息异常", zap.String("office", req.Office))
		return errors.New("提交的职场信息异常")
	}

	// 分解office名称
	officeID := officeSections[0]
	officeName := officeSections[1]

	// 初始化流程变量
	processVars := make(mapdata.MapData)

	// 初始化工单概览和工单详情
	orderAbstract := new(bpm.OrderAbstract)
	orderDetail := new(bpm.OrderDetail)

	// 生成工单标题
	orderTitle := p.generateOrderTitle(ctx, req.Title, processKey, string(req.OpType))

	// 把一些公共的内容先填写了
	orderAbstract.Title = orderTitle
	orderAbstract.Abstract = ""
	orderAbstract.ProcessKey = processKey
	orderAbstract.ProcessName = processName
	orderAbstract.ProcessCategory = bpm.ProProcessDesigner
	orderAbstract.BusinessID = openapi.GenerateBPMBusinessID(processPrefix)
	// 默认的提交的时候，状态是提交中，后续会由kafka提交过来的数据更新
	orderAbstract.ProcessState = bpm.Submit
	orderAbstract.InitiatorUserName = initiator
	orderAbstract.Description = req.Note
	orderAbstract.DesireDeliveryTime = req.DesireDeliveryTime
	orderAbstract.Office = officeName
	orderAbstract.OfficeID = officeID

	// 当前审批人以及审批人列表，默认一开始创建的时候，设置为空，后续会由kafka提交过来的数据更新
	orderAbstract.ApprovalUser = ""
	orderAbstract.ApprovalList = ""

	// 初始化时间戳
	current := time.Now().UnixMilli()
	orderAbstract.CreateTime = current
	orderAbstract.UpdateTime = current

	// 填写工单详情实例
	orderDetail.Title = orderTitle
	orderDetail.OpType = req.OpType
	orderDetail.BusinessID = orderAbstract.BusinessID
	orderDetail.CreateTime = current
	orderDetail.UpdateTime = current
	// 初始化业务数据
	var (
		bizData      any
		abstractInfo string
	)

	// 根据机房操作类型去执行不同的操作
	switch req.OpType {
	case bpmApi.OnRack:
		// 初始化上架需要填写设备的负责人
		if req.Owner == "" {
			zap.L().Error("设备上架，负责人不能为空")
			return errors.New("设备上架，负责人不能为空")
		}
		data := bpmApi.NewDCOnRackRequest()
		if err := RequestCheck(req.DCRequest, &data); err != nil {
			zap.L().Error("参数校验失败", zap.Error(err))
			return err
		}

		reinstall := false
		// 遍历数据，设置owner和是否需要重新安装，是否需要重装的分支
		for _, dev := range data {
			dev.Owner = req.Owner
			if dev.ReInstall {
				reinstall = true
			}
		}

		// 把数据转换为数据库中的结构
		dbData := make([]*bpmApi.DCOnRackDevice, 0)
		for _, dev := range data {
			var (
				idcRoom  string
				rackName string
			)

			if err := dev.Format(); err != nil {
				zap.L().Error("设备信息格式化失败", zap.Error(err))
				return err
			}

			// 因为传递的格式为{id}_{name}, 所以需要拆分, 等后面合并到一起以后，或者openapi完全后，可以去掉
			// 包括设备品牌和型号，其实都需要管理起来。现在是作为模型字段中的枚举属性存在的。
			// 而且想职场，机房，机柜这种还具备级联的属性，需要根据id来查询对应资源的信息
			// idc机房并不是必填项目，所以这里要判断一下
			if dev.IdcRoom != "" {
				idcRoom = strings.Split(dev.IdcRoom, "_")[1]
				if len(idcRoom) < 2 {
					zap.L().Error("机房信息异常")
					return errors.New("机房信息异常")
				}
			}

			// 机柜也是非必填，所以也有可能为空，需要提前判断一下。
			if dev.DeviceRack != "" {
				rackName = strings.Split(dev.DeviceRack, "_")[1]
				if len(rackName) < 2 {
					zap.L().Error("机柜信息异常")
					return errors.New("机柜信息异常")
				}
			}

			// 品牌和型号是必填项
			deviceBrand := strings.Split(dev.DeviceBrand[0], "@#")[1]
			if len(deviceBrand) < 2 {
				zap.L().Error("设备品牌异常")
				return errors.New("设备品牌信息异常")
			}
			deviceModel := strings.Split(dev.DeviceBrand[1], "@#")[1]
			if len(deviceModel) < 2 {
				zap.L().Error("设备型号信息异常")
				return errors.New("设备型号信息异常")
			}
			dbData = append(dbData, &bpmApi.DCOnRackDevice{
				DeviceType:      dev.DeviceType,
				DeviceSN:        dev.DeviceSN,
				DeviceBrand:     deviceBrand,
				DeviceModel:     deviceModel,
				ServiceInfo:     dev.ServiceInfo,
				OverdueTime:     dev.OverdueTime,
				Owner:           dev.Owner,
				IdcRoom:         idcRoom,
				DeviceRack:      rackName,
				DevicePosition:  dev.DevicePosition,
				Power:           dev.Power,
				DeviceHeight:    dev.DeviceHeight,
				HardWareMonitor: dev.HardWareMonitor,
				InterfaceBond:   dev.InterfaceBond,
				ReInstall:       dev.ReInstall,
			})
		}
		bizData = dbData
		// 构建摘要信息用于搜索
		for _, dt := range dbData {
			t := reflect.TypeOf(*dt)
			v := reflect.ValueOf(*dt)
			for i := 0; i < t.NumField(); i++ {
				field := t.Field(i)
				jsonTag := field.Tag.Get("json")
				fieldValue := utils.ToString(v.Field(i).Interface())
				if fieldValue == "" {
					continue
				}
				abstractInfo += fmt.Sprintf("%s:%s;",
					bpmApi.DcOnRackDeviceFieldName[jsonTag], fieldValue)
			}
			abstractInfo += "\n"
		}
		orderAbstract.Abstract = abstractInfo

		// 构建流程变量
		processVars.Set("reinstall", reinstall)
		processVars.Set("dc_type", bpmApi.OnRack)
	case bpmApi.OffRack:
		data := bpmApi.NewDCOffRackRequest()
		if err := RequestCheck(req.DCRequest, &data); err != nil {
			zap.L().Error("参数校验失败", zap.Error(err))
			return err
		}
		bizData = data
		deviceSNSet := make(map[string]bool, 0)
		for _, dt := range data {
			t := reflect.TypeOf(*dt)
			v := reflect.ValueOf(*dt)
			for i := 0; i < t.NumField(); i++ {
				field := t.Field(i)
				jsonTag := field.Tag.Get("json")
				fieldValue := utils.ToString(v.Field(i).Interface())
				if fieldValue == "" {
					continue
				}
				abstractInfo += fmt.Sprintf("%s:%s;",
					bpmApi.DcOffRackDeviceFieldName[jsonTag], fieldValue)
			}
			abstractInfo += "\n"
			deviceSNSet[dt.DeviceSN] = true
		}

		// 针对设备sn的合法性进行校验
		deviceSNList := make([]string, 0)
		for sn := range deviceSNSet {
			deviceSNList = append(deviceSNList, sn)
		}

		// 调用openapi获取设备信息
		resp, err := openapi.GetDeviceBySN(deviceSNList)
		if err != nil {
			zap.L().Error("调用openapi获取设备信息失败", zap.Error(err))
			return err
		}

		respSNMap := make(map[string]bool, 0)
		for _, resDevice := range resp {
			respSNMap[resDevice.DeviceSN] = true
		}

		// 校验设备是否存在
		for sn := range deviceSNSet {
			if _, ok := respSNMap[sn]; !ok {
				zap.L().Error("设备不存在", zap.String("device_sn", sn))
				return errors.New("设备不存在")
			}
		}

		orderAbstract.Abstract = abstractInfo
		processVars.Set("dc_type", bpmApi.OffRack)
	case bpmApi.ReInstall:
		if err := p.reinstall(ctx, initiator, req, orderTitle, orderAbstract, orderDetail); err != nil {
			zap.L().Error("重装流程失败", zap.Error(err))
			return err
		}
		return nil
	case bpmApi.Operation:
		data := bpmApi.NewDCOperationRequest()
		if err := RequestCheck(req.DCRequest, &data); err != nil {
			zap.L().Error("参数校验失败", zap.Error(err))
			return err
		}
		bizData = data
		for _, dt := range data {
			t := reflect.TypeOf(*dt)
			v := reflect.ValueOf(*dt)
			for i := 0; i < t.NumField(); i++ {
				field := t.Field(i)
				jsonTag := field.Tag.Get("json")
				fieldValue := utils.ToString(v.Field(i).Interface())
				if fieldValue == "" {
					continue
				}
				abstractInfo += fmt.Sprintf("%s:%s;",
					bpmApi.DcOperationDeviceFieldName[jsonTag], fieldValue)
			}
			abstractInfo += "\n"
		}
		orderAbstract.Abstract = abstractInfo
		processVars.Set("dc_type", bpmApi.Operation)
	case bpmApi.Cabling:
		data := bpmApi.NewDCCablingRequest()
		if err := RequestCheck(req.DCRequest, &data); err != nil {
			zap.L().Error("参数校验失败", zap.Error(err))
			return err
		}
		bizData = data
		for _, dt := range data {
			t := reflect.TypeOf(*dt)
			v := reflect.ValueOf(*dt)
			for i := 0; i < t.NumField(); i++ {
				field := t.Field(i)
				jsonTag := field.Tag.Get("json")
				fieldValue := utils.ToString(v.Field(i).Interface())
				if fieldValue == "" {
					continue
				}
				abstractInfo += fmt.Sprintf("%s:%s;", bpmApi.DcCablingDeviceFieldName[jsonTag], fieldValue)
			}
			abstractInfo += "\n"
		}
		orderAbstract.Abstract = abstractInfo
		processVars.Set("dc_type", bpmApi.Cabling)
	default:
		zap.L().Error("不合法的op_type, 请检查", zap.String("op_type", req.OpType.String()))
		return errors.New("不合法的op_type, 请检查")
	}

	// 把流程变量序列化
	processVarsBytes, err := json.Marshal(processVars)
	if err != nil {
		zap.L().Error("序列化流程变量失败", zap.Error(err))
		return err
	}
	orderAbstract.ProcessVars = processVarsBytes

	// 补充工单的数据内容
	dataBytes, err := json.Marshal(bizData)
	if err != nil {
		zap.L().Error("序列化工单数据失败", zap.Error(err))
		return err
	}
	orderDetail.BusinessData = dataBytes

	// TODO: 这里其实存在一个潜在的问题，就是这个发起工单和插入数据库其实是两步操作，但是是一个整体; 成功就都成功，失败就都失败
	// 也就是说他们是一个事务，但是由于目前的事务只能基于数据库内部的，当涉及到三方的就不好弄了。
	// 因为本质上, 比如kdb实现事务的是那个tx, 也就是依赖一个上下文，但是这个上下文openapi的操作其实是用不了的
	// 优先插入数据库，如果openapi创建失败，还可以使用kafka来补数据
	// 补数据：流程开发与管理平台 → 集成管理 → kafka消息管理，输入对应工单补充对应阶段数据即可。
	if err := p.store.BPMOrder().CreateProfessionalOrder(orderAbstract, orderDetail); err != nil {
		zap.L().Error("工单插入数据库失败", zap.Error(err))
		return err
	}
	zap.L().Debug("工单插入数据库成功")

	// 发起远程调用，调用openapi创建工单
	if _, err := openapi.ProcessStart(orderTitle, processKey, orderDetail.BusinessID, initiator, processVars); err != nil {
		zap.L().Error("发起流程失败", zap.Error(err))
		return err
	}

	zap.L().Info("发起流程调用OpenAPI成功",
		zap.String("order_title", orderTitle),
		zap.String("initiator", initiator),
		zap.String("order_business_id", orderDetail.BusinessID),
	)

	return nil
}

// GetProcessList 获取工单列表
func (p *processService) GetProcessList(ctx context.Context, orderType, keyword, operator string, pageNumber, pageSizeNumber int64) (*bpmApi.OrderListResponse, error) {
	zap.L().Debug("GetProcessList Service Called")
	span, _ := apm.StartSpan(ctx, "GetProcessList", "service")
	defer span.End()

	// 首先检查分页参数
	page := utils.ToInt(pageNumber)
	pageSize := utils.ToInt(pageSizeNumber)

	var (
		orderList []*bpm.OrderAbstract
		err       error
		total     int64
	)

	switch orderType {
	case "todo":
		// 待办
		orderList, total, err = p.store.BPMOrder().GetOrderListByTodo(operator, keyword, page, pageSize)
		if err != nil {
			zap.L().Error("获取待办工单列表失败", zap.Error(err))
			return nil, err
		}
	case "all":
		// 全部
		orderList, total, err = p.store.BPMOrder().GetOrderListByAll(operator, keyword, page, pageSize)
		if err != nil {
			zap.L().Error("获取全部工单列表失败", zap.Error(err))
			return nil, err
		}
	case "done":
		// 已办
		orderList, total, err = p.store.BPMOrder().GetOrderListByDone(operator, keyword, page, pageSize)
		if err != nil {
			zap.L().Error("获取已办工单列表失败", zap.Error(err))
			return nil, err
		}
	case "initiated":
		// 我发起的
		orderList, total, err = p.store.BPMOrder().GetOrderListByInitiator(operator, keyword, page, pageSize)
		if err != nil {
			zap.L().Error("获取我发起的工单列表失败", zap.Error(err))
			return nil, err
		}
	default:
		// 默认查询所有的工单
		orderList, total, err = p.store.BPMOrder().GetOrderListByAll(operator, keyword, page, pageSize)
		if err != nil {
			return nil, err
		}
	}

	data := make([]*bpmApi.OrderAbstractResponse, 0)

	// 批量获取IT机房操作流程的OpType信息，减少数据库查询次数
	dcProcessBusinessIDs := make([]string, 0)
	for _, o := range orderList {
		if o.ProcessCategory == bpm.ProProcessDesigner && o.ProcessKey == "ITDataCenterProcess" {
			dcProcessBusinessIDs = append(dcProcessBusinessIDs, o.BusinessID)
		}
	}

	// 批量查询IT机房操作流程的详情信息
	dcProcessOpTypes := make(map[string]bpmApi.DCOperationType)
	if len(dcProcessBusinessIDs) > 0 {
		dcProcessOpTypes = p.batchGetDCProcessOpTypes(dcProcessBusinessIDs)
	}

	for _, o := range orderList {
		// 根据流程类型和流程Key判断详情页类型
		detailPageType := p.determineDetailPageTypeOptimized(o.ProcessCategory, o.ProcessKey, o.BusinessID, dcProcessOpTypes)

		oar := &bpmApi.OrderAbstractResponse{
			Title:             o.Title,
			InitiatorUserName: o.InitiatorName,
			Abstract:          o.Abstract,
			ProcessState:      bpm.ProcessStateNameMap[o.ProcessState],
			Department:        o.InitiatorOrgName,
			BusinessID:        o.BusinessID,
			CreateTime:        o.CreateTime,
			ProcessKey:        o.ProcessKey,
			ProcessName:       o.ProcessName,
			ProcessType:       bpm.ProcessCategoryNameMap[o.ProcessCategory],
			URL:               o.Url,
			DetailPageType:    detailPageType,
		}
		data = append(data, oar)
	}

	// 补充分页信息
	resp := bpmApi.NewOrderListResponse()
	resp.Total = total
	resp.Data = data
	resp.PageSize = pageSizeNumber
	resp.CurrentPage = pageNumber
	pages := (total / pageSizeNumber) + 1
	resp.Pages = pages

	return resp, nil
}

// RejectDCProcess 驳回流程
func (p *processService) RejectDCProcess(ctx context.Context, initiator string, req *bpmApi.ProcessPassOrRejectRequest) (any, error) {
	zap.L().Debug("RejectDCProcess Service Called")
	span, _ := apm.StartSpan(ctx, "RejectDCProcess", "service")
	defer span.End()

	// 从数据库中获取工单，因为拒绝工单的时候，其实是根据工单当前所处的Node节点来进行拒绝的，而不是根据BusinessID
	order, err := p.store.BPMOrder().GetOrderByBusinessID(req.BusinessID)
	if err != nil {
		return nil, err
	}

	// 判断当前用户是否可以进行审批操作
	approvalUserList := strings.Split(order.ApprovalUser, ",")
	if !array.StringArray(approvalUserList).InArray(initiator) {
		return nil, errors.New("当前工单审批人不是您，无法进行驳回操作")
	}

	// 获取当前节点信息
	node, err := p.store.BPMNode().GetProcessNodeByNodeID(order.TaskKey)
	if err != nil {
		return nil, err
	}

	// 如果当前节点没有配置驳回后跳转的节点，则不支持驳回，一般是发起节点，结束节点，还有cmdb同步信息的节点
	if node.RejectTo == "none" && order.TaskName == node.NodeName {
		return nil, errors.New("当前节点不支持驳回")
	}

	// 这里targetNodeId在专业流程上的节点中进行了配置，所以这里就可以不传递了
	var openapiReq = &openapi.ProcessPassOrRejectRequest{
		Comments:     req.Comments,
		ProcessKey:   processKey,
		TaskId:       order.TaskID,
		UserId:       initiator,
		TargetNodeId: node.RejectTo,
	}

	resp, err := openapi.ProcessReject(openapiReq)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// parseDeviceData 将一条数据解析为可读的数据，用于前端展示
func parseDeviceData(content map[string]any, opType string) (map[string]any, error) {
	originalContent := make(map[string]any)
	switch opType {
	case string(bpmApi.OnRack):
		// 获取设备的k, v键值对
		for k, v := range content {
			// 如果k在DcOnRackDeviceFieldName中，则把v的值写入到field_tobe_supplement中
			originalContent[k] = map[string]any{
				"field_display_name": bpmApi.DcOnRackDeviceFieldName[k],
				"field_value":        v,
			}
		}
	case string(bpmApi.OffRack):
		for k, v := range content {
			originalContent[k] = map[string]any{
				"field_display_name": bpmApi.DcOffRackDeviceFieldName[k],
				"field_value":        v,
			}
		}
	case string(bpmApi.ReInstall):
		for k, v := range content {
			originalContent[k] = map[string]any{
				"field_display_name": bpmApi.DcReInstallDeviceFieldName[k],
				"field_value":        v,
			}
		}
	case string(bpmApi.Operation):
		for k, v := range content {
			originalContent[k] = map[string]any{
				"field_display_name": bpmApi.DcOperationDeviceFieldName[k],
				"field_value":        v,
			}
		}
	case string(bpmApi.Cabling):
		for k, v := range content {
			originalContent[k] = map[string]any{
				"field_display_name": bpmApi.DcCablingDeviceFieldName[k],
				"field_value":        v,
			}
		}
	default:
		zap.L().Error("当前工单类型不支持", zap.String("op_type", opType))
		return nil, errors.New("当前工单类型不支持")
	}

	return originalContent, nil
}

// GetProcessDetail 获取工单详情
func (p *processService) GetProcessDetail(ctx context.Context, businessId string) (any, error) {
	zap.L().Debug("GetProcessDetail Service Called")
	span, _ := apm.StartSpan(ctx, "GetProcessDetail", "service")
	defer span.End()

	// 初始化返回结构体
	resp := bpmApi.NewOrderDetailResponse()

	// 从数据库中获取工单
	order, err := p.store.BPMOrder().GetOrderByBusinessID(businessId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("工单不存在")
		}
		return nil, err
	}

	// 补充工单属性信息
	resp.ProcessState = order.ProcessState.String()
	resp.BusinessID = order.BusinessID
	resp.ProcessKey = order.ProcessKey
	resp.Department = order.InitiatorOrgName
	resp.CreateTime = order.CreateTime
	resp.Note = order.Description
	resp.Initiator = fmt.Sprintf("%s (%s)", order.InitiatorName, order.InitiatorUserName)
	resp.InitiatorUserName = order.InitiatorUserName
	resp.Title = order.Title

	// 首先要确定当前操作的分类是什么，因为分类不一样，所以对应的字段也不一样
	detail, err := p.store.BPMOrder().GetOrderDetailByBusinessID(businessId)
	if err != nil {
		return nil, err
	}
	resp.OpType = bpmApi.OpTypeNameMap[detail.OpType]

	// 首先获取当前工单的详情
	content, err := detail.ParseBusinessData()
	if err != nil {
		zap.L().Error("解析工单数据失败", zap.Error(err))
		return nil, err
	}

	// 如果说当前工单已经是结束状态了，那么这个时候直接返回工单详情即可
	// 因为结束状态的工单，已经没有后续的操作了
	if order.ProcessState == bpm.End {
		// 构建field_mapping用于拼接字段display_name和字段值给前端使用
		// 示例如下:
		// {
		// 	"device_sn": {
		// 		"field_display_name": "设备SN",
		// 		"field_value": "1234567890"
		// 	}
		// }
		dataContent := make([]any, 0)
		for _, dev := range content {
			originalContent, err := parseDeviceData(dev, detail.OpType.String())
			if err != nil {
				return nil, err
			}
			dataContent = append(dataContent, map[string]any{
				"data":                  originalContent,
				"field_tobe_supplement": make([]any, 0),
			})
		}

		resp.OrderContent = dataContent
		return resp, nil
	}

	// 当前的节点是什么
	nodeID := order.TaskKey
	if strings.TrimSpace(nodeID) == "" {
		zap.L().Error("当前工单节点信息为空")
		return nil, errors.New("当前工单节点信息为空, 请检查工单是否正常流转")
	}

	// 获取当前节点的详情
	node, err := p.store.BPMNode().GetProcessNodeByNodeID(nodeID)
	if err != nil {
		zap.L().Error("获取当前节点信息失败", zap.Error(err))
		return nil, err
	}

	// 根据当前的操作类型来确定当前工单的数据应该怎样序列化
	// 1. 我要做的是拿到已经填写的数据信息
	// 2. 以及要要填写的字段信息返回给前端
	// 因为可能要操作的是多台设备，所以返回的是一个数组
	// 看一下当前节点是否需要补充数据，如果需要补充数据，则需要返回当前节点需要补充的字段
	if !node.RequireData {
		// 如果当前节点不需要补充数据的话，那么直接返回当前工单的详情即可
		zap.L().Info("当前节点不需要补充数据")
		dataContent := make([]any, 0)

		for _, dev := range content {
			originalContent, err := parseDeviceData(dev, detail.OpType.String())
			if err != nil {
				return nil, err
			}
			dataContent = append(dataContent, map[string]any{
				"data":                  originalContent,
				"field_tobe_supplement": make([]any, 0),
			})
		}

		resp.OrderContent = dataContent
		return resp, nil
	}

	// 当前节点需要补充数据
	zap.L().Info("当前节点需要补充数据")

	// 当前节点需要补充字段的信息，全量字段信息，含服务器独占的
	fieldsTobeSupplement, err := p.store.BPMNode().GetProcessNodeFieldDefinitionByNodeID(nodeID)
	if err != nil {
		zap.L().Error("获取当前节点补充数据字段定义失败", zap.Error(err))
		return nil, err
	}

	// tip: 这里有可能是配置错误，一般不会出现这种情况，但是还是需要考虑一下
	if len(fieldsTobeSupplement) == 0 {
		zap.L().Info("当前节点不需要补充数据")
		return nil, errors.New("节点需要补充数据，但当前节点没有配置补充数据字段，请联系管理员")
	}

	// 对比查询每个节点可能需要返回地字段
	// TODO: 目前先写死了，因为只有上架需要补充字段，其他的都不需要
	switch detail.OpType {
	case bpmApi.OnRack:
		responseDevList := make([]bpmApi.DeviceInfoResponse, 0)
		// 遍历每个设备，对比每个设备需要返回的字段
		for _, device := range content {
			// 查看当前上架的到底是个啥
			deviceType := device["device_type"]
			// 重新整理需要返回地字段信息
			fields := make([]bpm.ProcessNodeFieldDefinition, 0)
			// 默认返回的字段是全量字段，包含一些服务器特有的字段，因此如果不是服务器设备的话，我们需要把服务器的字段滤掉
			for _, field := range fieldsTobeSupplement {
				// 服务器独占字段
				if field.ServerOnly && deviceType != "server" {
					continue
				}
				fields = append(fields, field)
			}

			// 初始化要返回的设备信息
			dev := bpmApi.NewDeviceInfoResponse()
			// 将用户已经提交的设备信息写入返回结构体
			originalContent, err := parseDeviceData(device, detail.OpType.String())
			if err != nil {
				return nil, err
			}
			dev.Data = originalContent
			// 查看每个设备需要补充的字段是否都进行了补充
			for _, field := range fields {
				// 不管之前有没有写过，这里都要放到补充信息的字段里
				f := new(bpmApi.FieldTobeSupplement)
				f.FieldName = field.FieldName
				f.FieldDisplayName = field.FieldDisplayName
				f.FieldType = bpm.FieldTypeMap[field.FieldType]
				f.FieldSeq = field.FieldSeq
				f.Required = utils.ToBool(field.Required)

				// 如果当前字段是下拉框字段，则需要返回下拉框的选项信息
				if field.FieldType == bpm.FieldSelect {
					// 声明一个新的field info
					fieldInfo := bpm.ProcessNodeFieldInfoSelect{}
					if err := json.Unmarshal(field.FieldInfo, &fieldInfo); err != nil {
						zap.L().Error("解析下拉框字段信息失败", zap.Error(err))
						return nil, err
					}

					// 填充selectField信息
					selectField := bpmApi.NewProcessNodeFieldTypeSelect()
					selectField.RequestURL = fieldInfo.RequestURL
					selectField.Method = fieldInfo.Method

					// 填充Options选项
					if len(fieldInfo.Options) > 0 {
						for _, option := range fieldInfo.Options {
							selectField.Options = append(selectField.Options, bpmApi.SelectOption{
								Label: option.Label,
								Value: option.Value,
							})
						}
					}

					// 如果要填写的参数长度大于0的话，我们还需要根据参数的依赖关系来填充参数
					if len(fieldInfo.Params) > 0 {
						if len(fieldInfo.RelyField) == 0 {
							// 如果依赖字段为空，则直接根据参数填充
							for _, param := range fieldInfo.Params {
								if param == "office_id" {
									selectField.Params["office_id"] = fmt.Sprintf("%s_%s", order.OfficeID, order.Office)
								} else {
									val, ok := device[param]
									if !ok && utils.CheckZero(val) {
										return nil, errors.New("当前设备信息不完整，缺少" + param + "字段")
									}
									selectField.Params[param] = val
								}
							}
						} else {
							// 如果依赖字段不为空，则根据依赖字段填充，这里仅返回要填充的字段，交由前端去填充，value补充为空字符串
							for _, param := range fieldInfo.Params {
								selectField.Params[param] = ""
							}
						}
					}

					// 填充依赖字段，这个依赖字段是根据数据库的配置填充的
					selectField.RelyField = fieldInfo.RelyField
					f.Attributes = selectField
				} else {
					f.Attributes = nil
				}

				// 看看之前提交的时候有没有写值
				fieldValue, ok := device[field.FieldName]
				// 如果提交的时候写过这个值，并且值不是零值，就把值返回给前端，否则返回空值
				if ok && !utils.CheckZero(fieldValue) {
					f.FieldValue = fieldValue
				} else {
					f.FieldValue = ""
				}
				dev.FieldTobeSupplement = append(dev.FieldTobeSupplement, f)
			}
			// 对补充字段进行排序
			sort.Sort(dev.FieldTobeSupplement)
			responseDevList = append(responseDevList, *dev)
		}
		resp.OrderContent = responseDevList
	case bpmApi.ReInstall:

	}

	return resp, nil
}

// PassDCProcess 同意工单
// TODO: 目前针对必填字段这里的判断写的和一坨屎一样，后面需要重新规划和设计，不过现在来不及了，写的就比较死了
func (p *processService) PassDCProcess(ctx context.Context, initiator string, req *bpmApi.ProcessPassRequest) error {
	zap.L().Debug("PassDCProcess Service Called")
	span, _ := apm.StartSpan(ctx, "PassDCProcess", "service")
	defer span.End()

	// 在这里我要先看一下，对应当前的这个工单所在的节点是否需要补充额外的字段
	order, err := p.store.BPMOrder().GetOrderByBusinessID(req.BusinessID)
	if err != nil {
		zap.L().Error("获取工单信息失败", zap.Error(err))
		return err
	}

	// 判断当前用户是否可以进行审批操作
	approvalUserList := strings.Split(order.ApprovalUser, ",")
	zap.L().Debug("当前审批人列表", zap.Any("审批人列表", approvalUserList), zap.String("当前用户", initiator))
	if !array.StringArray(approvalUserList).InArray(initiator) {
		zap.L().Error("当前工单审批人不是您，无法进行审批操作", zap.String("当前用户", initiator), zap.String("审批人列表", order.ApprovalUser))
		return errors.New("当前工单审批人不是您，无法进行审批操作")
	}

	// 获取当前工单的详情
	detail, err := p.store.BPMOrder().GetOrderDetailByBusinessID(req.BusinessID)
	if err != nil {
		zap.L().Error("获取工单详情失败", zap.Error(err))
		return err
	}

	// 查询当前的审批node节点
	node, err := p.store.BPMNode().GetProcessNodeByNodeID(order.TaskKey)
	if err != nil {
		zap.L().Error("获取当前工单的审批node节点失败", zap.Error(err))
		return err
	}

	// 如果不需要补充数据，则直接调用openapi进行提交
	if !node.RequireData {
		if _, err := openapi.ProcessPass(&openapi.ProcessPassOrRejectRequest{
			Comments:   req.Comments,
			ProcessKey: processKey,
			TaskId:     order.TaskID,
			UserId:     initiator,
		}); err != nil {
			zap.L().Error("发起流程失败", zap.Error(err))
			return err
		}
		zap.L().Info("通过工单成功")
		return nil
	}

	var (
		requiredFields = make([]bpm.ProcessNodeFieldDefinition, 0) // 必填字段
	)

	// 需要补充数据的字读都有哪些
	fieldsTobeSupplement, err := p.store.BPMNode().GetProcessNodeFieldDefinitionByNodeID(order.TaskKey)
	if err != nil {
		return err
	}
	if len(fieldsTobeSupplement) == 0 {
		return errors.New("节点需要补充数据，但是没有配置补充数据字段，请联系管理员")
	}

	// 记录必填字段
	for _, field := range fieldsTobeSupplement {
		if field.Required {
			requiredFields = append(requiredFields, field)
		}
	}

	// 用户可能压根没有提交，这个时候直接返回报错即可。
	if len(req.Data) == 0 {
		return errors.New("当前审批节点需要补充数据, 请检查提交的数据是否完整")
	}

	// 处理用户提交过来的数据
	userPostData, exists := req.Data.Get("devices")
	if !exists {
		return errors.New("当前审批节点需要补充设备信息, 请检查提交的数据是否完整")
	}
	userPostDataList := make([]map[string]any, 0)
	if err := mapstructure.Decode(userPostData, &userPostDataList); err != nil {
		zap.L().Error("反序列化用户提交的数据失败", zap.Error(err))
		return err
	}
	userPostDeviceMap := make(map[string]map[string]any)
	for _, upd := range userPostDataList {
		sn := utils.ToString(upd["device_sn"])
		if sn == "" {
			return errors.New("SN不能为空")
		}
		userPostDeviceMap[sn] = upd
	}

	// 解析工单中已经存储的数据
	bizData := make([]map[string]any, 0)
	if err := json.Unmarshal(detail.BusinessData, &bizData); err != nil {
		zap.L().Error("反序列化工单中已经存储的数据失败", zap.Error(err))
		return err
	}
	// 构建一个bizDataMap，其中key为设备的sn，value是设备的详细信息
	bizDataMap := make(map[string]map[string]any)
	for _, bd := range bizData {
		sn := utils.ToString(bd["sn"])
		if strings.TrimSpace(sn) == "" {
			zap.L().Error("SN不能为空")
			return errors.New("SN不能为空")
		}
		bizDataMap[sn] = bd
	}

	// 对比一下提交的和数据库中的设备数量是否一致
	if len(bizData) != len(userPostDeviceMap) {
		if len(userPostDeviceMap) == 0 {
			zap.L().Error("当前补充信息设备数量为0，请检查提交的数据是否完整")
			return errors.New("当前补充信息设备数量为0，请检查提交的数据是否完整")
		}
		zap.L().Error("当前补充信息设备数量与提交工单中的设备数量不一致")
		return errors.New("当前补充信息设备数量与提交工单中的设备数量不一致")
	}

	// 避免用户漏提设备
	for k := range bizDataMap {
		if _, ok := userPostDeviceMap[k]; !ok {
			zap.L().Error("设备SN [" + k + "] 没有提交")
			return errors.New("设备SN [" + k + "] 没有提交")
		}
	}

	// 目前逻辑先简单实现，目前只有上架和装机的时候需要补充装机信息
	switch detail.OpType {
	case bpmApi.OnRack:
		for _, ud := range userPostDeviceMap {
			// 单独处理机房和机柜
			if idcRoom, ok := ud["idc_room"]; ok {
				room := utils.ToString(idcRoom)
				if room == "" {
					return errors.New("机房不能为空")
				}
				roomSections := strings.Split(room, "_")
				if len(roomSections) != 2 {
					zap.L().Error("机房格式错误", zap.String("room", room))
					return errors.New("机房格式错误")
				}
				roomName := roomSections[1]
				ud["idc_room"] = roomName
			}

			// 单独处理机柜
			if deviceRack, ok := ud["device_rack"]; ok {
				rack := utils.ToString(deviceRack)
				if rack == "" {
					return errors.New("机柜不能为空")
				}
				rackSections := strings.Split(rack, "_")
				if len(rackSections) != 2 {
					zap.L().Error("机柜格式错误", zap.String("rack", rack))
					return errors.New("机柜格式错误")
				}
				rackName := rackSections[1]
				ud["device_rack"] = rackName
			}
		}

		// 逐一比对提交的每一台设备, 是不是必填的字段都提交了
		for sn, device := range bizDataMap {
			// 取出当前设备的类型
			deviceType := utils.ToString(device["device_type"])
			for _, requiredField := range requiredFields {
				// 如果当前这台设备并不是服务器设备，但是字段是服务器字段的话，那么就可以跳过这个字段的验证
				if deviceType != "server" && requiredField.ServerOnly {
					continue
				}
				// 首先看能不能拿到对应的字段
				val, ok := userPostDeviceMap[sn][requiredField.FieldName]
				// 如果拿不到对应的字段的话，那么就直接报错
				if !ok {
					return fmt.Errorf("设备%s信息不完整，%s字段必填，不能为空", strings.ToUpper(sn), requiredField.FieldName)
				}
				// 如果可以拿到对应的字段的话，还得看是不是零值
				if utils.CheckZero(val) {
					return fmt.Errorf("设备%s信息不完整，%s字段必填，不能为空", strings.ToUpper(sn), requiredField.FieldName)
				}
			}
		}

		// 提交过来如果没问题还要更新回数据库
		updatedDevices := make([]map[string]any, 0)
		for sn, device := range bizDataMap {
			userPostDevice, ok := userPostDeviceMap[sn]
			if !ok {
				return errors.New("当前提交的设备信息与数据库中的设备信息不一致")
			}
			// 遍历用户提交过来的所有数据，写回devices
			for k, v := range userPostDevice {
				device[k] = v
			}
			updatedDevices = append(updatedDevices, device)
		}
		// 更新数据库
		bizData = updatedDevices

		// 先更新摘要，这里直接依照最新的值解析结果就可以了
		abstractInfo := ""
		for _, dm := range updatedDevices {
			for k, v := range dm {
				if utils.ToString(v) == "" {
					continue
				}
				abstractInfo += fmt.Sprintf("%s:%s;",
					bpmApi.DcOnRackDeviceFieldName[k], v)
			}
			abstractInfo += "\n"
		}
		order.Abstract = abstractInfo
		if err := p.store.BPMOrder().UpdateOrder(req.BusinessID, order); err != nil {
			return err
		}

		jsonBytes, err := json.Marshal(bizData)
		if err != nil {
			zap.L().Error(err.Error())
			return err
		}
		detail.BusinessData = jsonBytes
		if err := p.store.BPMOrder().UpdateOrderDetail(req.BusinessID, detail); err != nil {
			return err
		}
	case bpmApi.ReInstall:
		// 逐一比对提交的每一台设备, 是不是必填的字段都提交了
		for sn := range bizDataMap {
			for _, requiredField := range requiredFields {
				val, ok := userPostDeviceMap[sn][requiredField.FieldName]
				if !ok || utils.CheckZero(val) {
					return fmt.Errorf("设备%s信息不完整，%s字段必填，不能为空", strings.ToUpper(sn), requiredField.FieldName)
				}
			}
		}

		osInstallMap := make(map[string]*bpm.InstallConfig)
		for sn, device := range bizDataMap {
			ins := &bpm.InstallConfig{}
			configMap, ok := device["config"]
			if !ok {
				zap.L().Error("设备信息中没有config字段", zap.String("sn", sn))
				return errors.New("设备信息中没有config字段")
			}
			jsonBytes, err := json.Marshal(configMap)
			if err != nil {
				zap.L().Error("序列化设备信息失败", zap.Error(err))
				return err
			}
			if err := json.Unmarshal(jsonBytes, ins); err != nil {
				zap.L().Error("反序列化设备信息失败", zap.Error(err))
				return err
			}

			osInstallMap[sn] = ins
		}

		// 提交过来如果没问题还要更新回数据库
		updatedDevices := make([]map[string]any, 0)
		for sn, device := range bizDataMap {
			userPostDevice, ok := userPostDeviceMap[sn]
			if !ok {
				return errors.New("当前提交的设备信息与数据库中的设备信息不一致")
			}
			installConfig, ok := osInstallMap[sn]
			if !ok {
				zap.L().Error("设备信息中没有install_config字段", zap.String("sn", sn))
				return errors.New("设备信息中没有install_config字段")
			}
			// 通过用户提交的Post信息来更新装机信息
			ipAddr, ok := userPostDevice["ip_address"]
			if ok {
				installConfig.Network.SystemIP = utils.ToString(ipAddr)
			}
			mask, ok := userPostDevice["ip_netmask"]
			if ok {
				installConfig.Network.SystemMask = utils.ToString(mask)
			}
			gateway, ok := userPostDevice["ip_gateway"]
			if ok {
				installConfig.Network.SystemGW = utils.ToString(gateway)
			}
			bmcIP, ok := userPostDevice["bmc_ip_address"]
			if ok {
				installConfig.BMC.BMCIP = utils.ToString(bmcIP)
			}
			bmcNetMask, ok := userPostDevice["bmc_netmask"]
			if ok {
				installConfig.BMC.BMCMask = utils.ToString(bmcNetMask)
			}
			bmcGW, ok := userPostDevice["bmc_gateway"]
			if ok {
				installConfig.BMC.BMCGW = utils.ToString(bmcGW)
			}
			device["config"] = installConfig
			updatedDevices = append(updatedDevices, device)
		}

		bizData = updatedDevices

		// 更新摘要
		abstractInfo := ""
		for _, dm := range bizData {
			for k, v := range dm {
				if utils.ToString(v) == "" {
					continue
				}
				abstractInfo += fmt.Sprintf("%s:%s;", bpmApi.DcReInstallDeviceFieldName[k], v)
			}
			abstractInfo += "\n"
		}
		order.Abstract = abstractInfo

		// 更新工单概览
		if err := p.store.BPMOrder().UpdateOrder(req.BusinessID, order); err != nil {
			zap.L().Error("更新工单概览失败", zap.Error(err))
			return err
		}

		// 更新工单详情
		jsonBytes, err := json.Marshal(bizData)
		if err != nil {
			zap.L().Error(err.Error())
			return err
		}
		detail.BusinessData = jsonBytes

		// 更新工单详情
		if err := p.store.BPMOrder().UpdateOrderDetail(req.BusinessID, detail); err != nil {
			zap.L().Error("更新工单详情失败", zap.Error(err))
			return err
		}

		// 是否要发起自动重装任务
		if node.NodeID == "it_dc_reinstall_evaluation" {
			if err := p.initiateAutoReInstall(ctx, req.BusinessID); err != nil {
				return err
			}
		}
	default:
		zap.L().Error("当前工单类型不支持补充设备信息", zap.String("工单类型", detail.OpType.String()))
		return errors.New("当前工单类型不支持补充设备信息, 请联系管理员")
	}

	// 调用openapi直接进行提交
	if _, err := openapi.ProcessPass(&openapi.ProcessPassOrRejectRequest{
		Comments:   req.Comments,
		ProcessKey: processKey,
		TaskId:     order.TaskID,
		UserId:     initiator,
	}); err != nil {
		return err
	}

	return nil
}

// GetOrderAuditRecord 获取工单审批记录
func (p *processService) GetOrderAuditRecord(ctx context.Context, businessId string) (*bpmApi.OrderAuditRecordResponse, error) {
	zap.L().Debug("GetOrderAuditRecord Service Called")
	span, _ := apm.StartSpan(ctx, "GetOrderAuditRecord", "service")
	defer span.End()

	// 查询工单审批记录
	openapiResp, err := openapi.ProcessDetail(businessId)
	if err != nil {
		return nil, err
	}

	// 初始化返回结构体
	resp := bpmApi.NewOrderAuditRecordResponse()

	// 先填写一些标准化的信息
	resp.BusinessID = openapiResp.BusinessID
	resp.ProcessKey = openapiResp.ProcessDetailInfo.ProcessKey
	resp.ProcessName = openapiResp.ProcessDetailInfo.WorkFlowName
	resp.Title = openapiResp.ProcessDetailInfo.ApplyExplain
	resp.ProcessState = openapiResp.ProcessDetailInfo.ProcessState
	resp.ProcessStateName = openapiResp.ProcessDetailInfo.ProcessStateName

	// 遍历审批记录，写入返回结构体
	// 默认接口返回的记录是最新的在前，最旧的在后
	for _, record := range openapiResp.ProcessDetailInfo.DetailTasks {
		resp.Data = append(resp.Data, bpmApi.OrderAuditRecord{
			TaskID:          record.TaskID,
			ApproveTime:     record.ApproveTime,
			Comments:        record.Comments,
			Operation:       record.Operation,
			OperationName:   record.OperationName,
			OperationResult: record.OperationResult,
			TaskName:        record.TaskName,
			UserName:        record.UserName,
		})
	}

	return resp, nil
}

// ProcessBack 申请人退回流程
func (p *processService) ProcessBack(ctx context.Context, initiator string, req *bpmApi.ProcessPassOrRejectRequest) error {
	zap.L().Debug("ProcessBack Service Called")
	span, _ := apm.StartSpan(ctx, "ProcessBack", "service")
	defer span.End()

	// 先把当前要操作的工单给查出来
	order, err := p.store.BPMOrder().GetOrderByBusinessID(req.BusinessID)
	if err != nil {
		return err
	}

	// 判断当前用户是否可以进行退回操作
	approvalUserList := strings.Split(order.ApprovalUser, ",")
	if !array.StringArray(approvalUserList).InArray(initiator) {
		return errors.New("当前工单审批人不是您，无法进行退回操作")
	}

	// 首先判断当前操作人是否是申请人
	if initiator != order.InitiatorUserName {
		return errors.New("当前用户不是申请人，无法进行退回操作")
	}

	// 调用openapi进行退回
	openapiReq := &openapi.ProcessBackRequest{
		TaskDefKey: order.TaskKey,
		BackReason: req.Comments,
		Username:   initiator,
		BusinessId: req.BusinessID,
	}
	if _, err := openapi.ProcessBack(openapiReq); err != nil {
		return err
	}

	return nil
}

// FrontAddSign 前加签
func (p *processService) FrontAddSign(ctx context.Context, initiator string, req *bpmApi.ProcessAddOrShiftSignRequest) error {
	zap.L().Debug("FrontAddSign Service Called")
	span, _ := apm.StartSpan(ctx, "FrontAddSign", "service")
	defer span.End()

	// 先查出来当前的工单
	order, err := p.store.BPMOrder().GetOrderByBusinessID(req.BusinessID)
	if err != nil {
		return err
	}

	// 判断当前用户是否可以进行加签操作
	approvalUserList := strings.Split(order.ApprovalUser, ",")
	if !array.StringArray(approvalUserList).InArray(initiator) {
		return errors.New("当前工单审批人不是您，无法进行加签操作")
	}

	openapiReq := &openapi.AddSignRequest{
		ProcessKey:   order.ProcessKey,
		Comments:     req.Comments,
		Assignee:     req.UserName,
		TargetNodeId: BPMNodeApplication, // 加签被拒绝后，默认回到申请节点
		UserId:       initiator,
		TaskId:       order.TaskID,
	}

	if _, err := openapi.FrontAddSign(openapiReq); err != nil {
		return err
	}

	return nil
}

// AfterAddSign 后加签
func (p *processService) AfterAddSign(ctx context.Context, initiator string, req *bpmApi.ProcessAddOrShiftSignRequest) error {
	zap.L().Debug("AfterAddSign Service Called")
	span, _ := apm.StartSpan(ctx, "AfterAddSign", "service")
	defer span.End()

	// 先查出来当前的工单
	order, err := p.store.BPMOrder().GetOrderByBusinessID(req.BusinessID)
	if err != nil {
		return err
	}

	// 判断当前用户是否可以进行加签操作
	approvalUserList := strings.Split(order.ApprovalUser, ",")
	if !array.StringArray(approvalUserList).InArray(initiator) {
		return errors.New("当前工单审批人不是您，无法进行加签操作")
	}

	// 调用openapi进行后加签
	openapiReq := &openapi.AddSignRequest{
		ProcessKey:   order.ProcessKey,
		Comments:     req.Comments,
		Assignee:     req.UserName,
		TargetNodeId: BPMNodeApplication, // 加签被拒绝后，默认回到申请节点
		UserId:       initiator,
		TaskId:       order.TaskID,
	}

	if _, err := openapi.AfterAddSign(openapiReq); err != nil {
		return err
	}

	return nil
}

// ShiftSign 转签
func (p *processService) ShiftSign(ctx context.Context, initiator string, req *bpmApi.ProcessAddOrShiftSignRequest) error {
	zap.L().Debug("ShiftSign Service Called")
	span, _ := apm.StartSpan(ctx, "ShiftSign", "service")
	defer span.End()

	order, err := p.store.BPMOrder().GetOrderByBusinessID(req.BusinessID)
	if err != nil {
		return err
	}

	// 判断当前用户是否可以进行转签操作
	approvalUserList := strings.Split(order.ApprovalUser, ",")
	if !array.StringArray(approvalUserList).InArray(initiator) {
		return errors.New("当前工单审批人不是您，无法进行转签操作")
	}

	oldApprovalUser := strings.Split(order.ApprovalUser, ",")
	if len(oldApprovalUser) == 0 {
		return errors.New("当前工单没有审批人，无法进行转签操作, 请联系管理员")
	}

	if !array.InArray(initiator, oldApprovalUser) {
		return errors.New("当前用户不是审批人，无法进行转签操作")
	}

	// 调用openapi进行转签
	openapiReq := &openapi.ShiftSignRequest{
		Assignee:    req.UserName,           // 转签的目标是谁
		Comments:    req.Comments,           // 转签的原因是啥
		OldAssignee: initiator,              // 旧审批人是谁，一般是当前用户，不然你也操作不了这个单子
		TaskIds:     []string{order.TaskID}, // 当前工单的taskID
		UserId:      initiator,              // 发起人，但是目前来看转签的时候，这个字段可以不传也不影响转签成功
	}

	if _, err := openapi.ShiftSign(openapiReq); err != nil {
		return err
	}

	return nil
}

// Consult 征询
func (p *processService) Consult(ctx context.Context, initiator string, req *bpmApi.ProcessAddOrShiftSignRequest) error {
	zap.L().Debug("Consult Service Called")
	span, _ := apm.StartSpan(ctx, "Consult", "service")
	defer span.End()

	order, err := p.store.BPMOrder().GetOrderByBusinessID(req.BusinessID)
	if err != nil {
		return err
	}

	// 判断当前用户是否可以进行征询操作
	approvalUserList := strings.Split(order.ApprovalUser, ",")
	if !array.StringArray(approvalUserList).InArray(initiator) {
		return errors.New("当前工单审批人不是您，无法进行征询操作")
	}

	openapiReq := &openapi.ConsultRequest{
		ProcessKey: order.ProcessKey,
		Comments:   req.Comments,
		Assignee:   req.UserName,
		TaskId:     order.TaskID,
		UserId:     initiator,
	}

	if _, err := openapi.Consult(openapiReq); err != nil {
		return err
	}

	return nil
}

// Inform 知会
func (p *processService) Inform(ctx context.Context, initiator string, req *bpmApi.ProcessInformRequest) error {
	zap.L().Debug("Inform Service Called")
	span, _ := apm.StartSpan(ctx, "Inform", "service")
	defer span.End()

	openapiReq := &openapi.ProcessInformRequest{
		BusinessID:         req.BusinessID,
		Comments:           req.Comments,
		NoticeUserNameList: req.NoticeUserList,
		UserName:           initiator,
	}

	if _, err := openapi.ProcessInform(openapiReq); err != nil {
		return err
	}
	return nil
}

// StartDCProcess 启动工单, 一般用于工单被撤回后，重新发起新的工单。
func (p *processService) StartDCProcess(ctx context.Context, initiator string, req *bpmApi.ProcessStartRequest) error {
	zap.L().Debug("StartDCProcess Service Called")
	span, _ := apm.StartSpan(ctx, "StartDCProcess", "service")
	defer span.End()

	// 先把工单查出来
	order, err := p.store.BPMOrder().GetOrderByBusinessID(req.BusinessID)
	if err != nil {
		zap.L().Error("查询工单失败", zap.Error(err), zap.String("工单ID", req.BusinessID))
		return err
	}

	// 判断当前用户是否可以进行启动操作
	approvalUserList := strings.Split(order.ApprovalUser, ",")
	if !array.StringArray(approvalUserList).InArray(initiator) {
		zap.L().Error("当前工单审批人不是您，无法启用流程", zap.String("工单ID", req.BusinessID), zap.String("审批人", initiator))
		return errors.New("当前工单审批人不是您，无法启用流程")
	}

	// 判断标题，如果用户传递了标题，那么就用用户的标题，否则就用工单的标题，保持原样
	title := ""
	if req.Title != "" {
		title = req.Title
	} else {
		title = order.Title
	}

	// 重新发起工单需要把之前的流程变量带过来
	processVars := make(mapdata.MapData)
	if err := json.Unmarshal(order.ProcessVars, &processVars); err != nil {
		zap.L().Error("解析工单流程变量失败", zap.Error(err), zap.String("工单ID", req.BusinessID))
		return err
	}

	// 打印工单流程变量
	zap.L().Debug("工单流程变量", zap.Any("processVars", processVars))

	// 打印原始的ProcessVars字节数据，用于调试
	zap.L().Debug("原始ProcessVars字节数据", zap.String("raw_data", string(order.ProcessVars)))

	// 调用openapi启动工单
	zap.L().Info("准备调用ProcessStart",
		zap.String("title", title),
		zap.String("processKey", order.ProcessKey),
		zap.String("businessID", req.BusinessID),
		zap.String("initiator", initiator),
		zap.Any("processVars", processVars))

	if _, err := openapi.ProcessStart(title, order.ProcessKey, req.BusinessID, initiator, processVars); err != nil {
		zap.L().Error("启动工单失败", zap.Error(err), zap.String("工单ID", req.BusinessID))
		return err
	}

	zap.L().Info("ProcessStart调用成功", zap.String("工单ID", req.BusinessID))

	return nil
}

// CancelDCProcess 撤销工单
func (p *processService) CancelDCProcess(ctx context.Context, initiator string, req *bpmApi.ProcessPassOrRejectRequest) error {
	zap.L().Debug("CancelDCProcess Service Called")
	span, _ := apm.StartSpan(ctx, "CancelDCProcess", "service")
	defer span.End()

	o, err := p.store.BPMOrder().GetOrderByBusinessID(req.BusinessID)
	if err != nil {
		return err
	}

	// 判断当前用户是否可以进行撤销操作
	approvalUserList := strings.Split(o.ApprovalUser, ",")
	if !array.StringArray(approvalUserList).InArray(initiator) {
		return errors.New("当前工单审批人不是您，无法进行撤销操作")
	}

	if initiator != o.InitiatorUserName {
		return errors.New("当前用户不是申请人，无法进行撤销操作")
	}

	openapiReq := &openapi.ProcessCancelRequest{
		BusinessID: req.BusinessID,
		Comments:   req.Comments,
		UserId:     initiator,
	}

	if _, err := openapi.ProcessCancel(openapiReq); err != nil {
		return err
	}

	return nil
}

// GetOrderInfo 获取工单详情
func (p *processService) GetOrderInfo(ctx context.Context, businessId string) (any, error) {
	zap.L().Debug("GetOrderInfo Service Called")
	span, _ := apm.StartSpan(ctx, "GetOrderInfo", "service")
	defer span.End()

	// 查询工单摘要
	order, err := p.store.BPMOrder().GetOrderByBusinessID(businessId)
	if err != nil {
		zap.L().Error("查询工单摘要失败", zap.Error(err), zap.String("工单ID", businessId))
		return nil, err
	}

	// 查询工单详情
	detail, err := p.store.BPMOrder().GetOrderDetailByBusinessID(businessId)
	if err != nil {
		zap.L().Error("查询工单详情失败", zap.Error(err), zap.String("工单ID", businessId))
		return nil, err
	}

	// 构建Response
	resp := bpmApi.NewOrderInfoResponse()
	resp.InitiatorUserName = order.InitiatorUserName
	resp.InitiatorName = order.InitiatorName
	resp.Department = order.InitiatorOrgName
	resp.Note = order.Description
	resp.OpType = bpmApi.OpTypeNameMap[detail.OpType]

	switch detail.OpType {
	case bpmApi.OnRack:
		data := make([]bpmApi.DCOnRackDevice, 0)
		if err := json.Unmarshal(detail.BusinessData, &data); err != nil {
			return nil, err
		}
		resp.OrderContent = data
		resp.FieldMapping = bpmApi.DcOnRackDeviceFieldName
	case bpmApi.OffRack:
		data := make([]bpmApi.DCOffRackDevice, 0)
		if err := json.Unmarshal(detail.BusinessData, &data); err != nil {
			return nil, err
		}
		resp.OrderContent = data
		resp.FieldMapping = bpmApi.DcOffRackDeviceFieldName
	case bpmApi.ReInstall:
		installInfos, err := p.store.InstallOS().GetInstallInfoByBusinessID(ctx, businessId)
		if err != nil {
			return nil, err
		}

		if len(installInfos) == 0 {
			return nil, errors.New("安装信息不存在")
		}

		message := make([]bpmApi.DCReInstallDevice, 0)
		for _, inst := range installInfos {
			config := new(bpm.InstallConfig)
			if err := json.Unmarshal(inst.Config, config); err != nil {
				return nil, err
			}

			message = append(message, bpmApi.DCReInstallDevice{
				DeviceSN:     inst.SN,
				ServiceInfo:  order.Description,
				SystemFamily: bpmApi.SystemFamily(config.SysFamily.OsName),
				RaidGroup:    bpmApi.RaidGroup(config.Raid.Info.SystemRaidLevel),
				BMCIPAddress: config.BMC.BMCIP,
				BMCNetMask:   config.BMC.BMCMask,
				BMCGateway:   config.BMC.BMCGW,
				IPAddress:    config.Network.SystemIP,
				IPNetMask:    config.Network.SystemMask,
				IPGateway:    config.Network.SystemGW,
				IPDNS:        config.Network.SystemDNS,
			})
		}
		resp.OrderContent = message
		resp.FieldMapping = bpmApi.DcReInstallDeviceFieldName
	case bpmApi.Operation:
		data := make([]bpmApi.DCOperationDevice, 0)
		if err := json.Unmarshal(detail.BusinessData, &data); err != nil {
			return nil, err
		}
		resp.OrderContent = data
		resp.FieldMapping = bpmApi.DcOperationDeviceFieldName
	case bpmApi.Cabling:
		data := make([]bpmApi.DCCablingRequest, 0)
		if err := json.Unmarshal(detail.BusinessData, &data); err != nil {
			return nil, err
		}
		for idx, cable := range data {
			parts := strings.Split(cable.IdcRoom, "_")
			if len(parts) == 2 {
				data[idx].IdcRoom = parts[1]
			}
		}
		resp.OrderContent = data
		resp.FieldMapping = bpmApi.DcCablingDeviceFieldName
	}

	return resp, nil
}

// GetOrderBase 获取工单基本信息, 只包含工单编号，申请人，备注，工单类型，申请时间，标题，期望交付时间
func (p *processService) GetOrderBase(ctx context.Context, businessId string) (*message.OrderBaseMessage, error) {
	zap.L().Debug("GetOrderBase Service Called")
	span, _ := apm.StartSpan(ctx, "GetOrderBase", "service")
	defer span.End()

	// 查询工单摘要
	order, err := p.store.BPMOrder().GetOrderByBusinessID(businessId)
	if err != nil {
		return nil, err
	}

	// 只有activityID为空，才证明是真正的审批节点，而不是自动任务执行阶段
	approvers := make([]string, 0)
	if order.ActivityID == "" {
		approversList := strings.Split(order.ApprovalUser, ",")
		for _, approver := range approversList {
			if strings.TrimSpace(approver) != "" {
				approvers = append(approvers, approver)
			}
		}
	}

	detail, err := p.store.BPMOrder().GetOrderDetailByBusinessID(businessId)
	if err != nil {
		return nil, err
	}

	dcProcessOpTypes := p.batchGetDCProcessOpTypes([]string{businessId})

	// Format DesireDeliveryTime from "2025-07-06T16:00:00.000Z" to "YYYY-MM-DD hh:mm:ss"
	desireDeliveryTime := order.DesireDeliveryTime
	if parsedTime, err := time.Parse(time.RFC3339, desireDeliveryTime); err == nil {
		desireDeliveryTime = parsedTime.Format("2006-01-02 15:04:05")
	}

	resp := &message.OrderBaseMessage{
		BusinessID:         order.BusinessID,
		Initiator:          order.InitiatorUserName,
		Description:        order.Description,
		OperationType:      bpmApi.OpTypeNameMap[detail.OpType],
		CreatedAt:          order.CreateTime,
		Title:              order.Title,
		DesireDeliveryTime: desireDeliveryTime,
		Approvers:          approvers,
		ProcessState:       order.ProcessState.String(),
		TaskKey:            order.TaskKey,
		DetailPageType: p.determineDetailPageTypeOptimized(
			order.ProcessCategory,
			order.ProcessKey,
			businessId,
			dcProcessOpTypes,
		),
	}

	return resp, nil
}

// batchGetDCProcessOpTypes 批量获取IT机房操作流程的OpType信息
func (p *processService) batchGetDCProcessOpTypes(businessIDs []string) map[string]bpmApi.DCOperationType {
	result := make(map[string]bpmApi.DCOperationType)

	if len(businessIDs) == 0 {
		return result
	}

	// 这里直接使用store接口来批量查询，避免依赖内部实现
	for _, businessID := range businessIDs {
		if detail, err := p.store.BPMOrder().GetOrderDetailByBusinessID(businessID); err == nil {
			result[businessID] = detail.OpType
		}
	}

	return result
}

// determineDetailPageTypeOptimized 优化的详情页类型判断方法
func (p *processService) determineDetailPageTypeOptimized(category bpm.ProcessCategory, processKey, businessID string, dcProcessOpTypes map[string]bpmApi.DCOperationType) string {
	// 快流程直接返回空
	if category == bpm.AgileProcessDesigner {
		return ""
	}

	// 非IT机房操作流程返回空
	if processKey != "ITDataCenterProcess" {
		return ""
	}

	// 从批量查询结果中获取OpType
	opType, exists := dcProcessOpTypes[businessID]
	if !exists {
		return ""
	}

	// 如果是重装类型，返回重装详情页
	if opType == bpmApi.ReInstall {
		return string(bpmApi.DetailPageTypeDCReinstall)
	}

	// 其他操作类型返回空字符串
	return ""
}
